import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import React, { useEffect, useRef } from 'react';

async function registerForPushNotificationsAsync() {
  let token;
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }
  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  if (finalStatus !== 'granted') {
    alert('Failed to get push token for push notification!');
    return;
  }
  token = (await Notifications.getExpoPushTokenAsync()).data;
  return token;
}

export default function App() {
  useEffect(() => {
    registerForPushNotificationsAsync().then(token => {
      if (token) {
        console.log('Expo Push Token:', token);
        // TODO: Send this token to your backend for this user
      }
    });
    const subscription = Notifications.addNotificationReceivedListener(notification => {
      // Optionally update in-app notification state here
      console.log('Notification received:', notification);
    });
    return () => subscription.remove();
  }, []);
} 