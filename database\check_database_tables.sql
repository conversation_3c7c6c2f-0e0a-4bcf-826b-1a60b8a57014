-- Check Database Tables and RLS Status
-- Run this first to see what tables exist in your database

-- 1. List all tables in your database
SELECT 
    table_name,
    CASE 
        WHEN table_name = 'users' THEN '👤 User accounts'
        WHEN table_name = 'videos' THEN '🎬 Video content'
        WHEN table_name = 'subscriptions' THEN '💳 User subscriptions'
        WHEN table_name = 'purchases' THEN '🛒 Video purchases'
        WHEN table_name = 'comments' THEN '💬 Video comments'
        WHEN table_name = 'likes' THEN '❤️ Video likes'
        WHEN table_name = 'follows' THEN '👥 User follows'
        WHEN table_name = 'views' THEN '👁️ Video views'
        WHEN table_name = 'notifications' THEN '🔔 User notifications'
        WHEN table_name = 'reports' THEN '🚨 Content reports'
        WHEN table_name = 'payouts' THEN '💰 Creator payouts'
        ELSE '📋 Other table'
    END as description
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- 2. Check RLS status for each table
SELECT 
    schemaname,
    tablename,
    CASE 
        WHEN rowsecurity THEN '🔒 RLS Enabled'
        ELSE '⚠️ RLS Disabled'
    END as rls_status
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 3. Check existing policies on users table
SELECT 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'INSERT' THEN '➕ Insert (Registration)'
        WHEN cmd = 'SELECT' THEN '👁️ Select (Read)'
        WHEN cmd = 'UPDATE' THEN '✏️ Update (Modify)'
        WHEN cmd = 'DELETE' THEN '🗑️ Delete (Remove)'
        WHEN cmd = 'ALL' THEN '🔧 All Operations'
        ELSE cmd
    END as operation_type,
    qual as condition_check
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY cmd, policyname;

-- 4. Check if users table has the required columns
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Check auth schema (Supabase auth tables)
SELECT 
    table_name
FROM information_schema.tables 
WHERE table_schema = 'auth'
ORDER BY table_name;
