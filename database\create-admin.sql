-- Create Admin User for GoGo App
-- Run this in Supabase SQL Editor after setting up authentication

-- Option 1: Update the default admin user with a specific UUID
-- (Replace 'your-auth-user-uuid' with the actual UUID from Supabase Auth)
/*
UPDATE users 
SET id = 'your-auth-user-uuid'
WHERE email = '<EMAIL>';
*/

-- Option 2: Create a new admin user
-- (Replace with your preferred admin credentials)
INSERT INTO users (email, username, full_name, role) VALUES
('<EMAIL>', 'myadmin', 'My Admin Name', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Option 3: Promote an existing user to admin
-- (Replace '<EMAIL>' with actual email)
/*
UPDATE users 
SET role = 'admin' 
WHERE email = '<EMAIL>';
*/

-- Verify admin users
SELECT id, email, username, full_name, role, created_at 
FROM users 
WHERE role = 'admin';

-- Note: After running this SQL, you need to:
-- 1. Go to Supabase Authentication > Users
-- 2. Create an auth user with the same email
-- 3. Set a password for the auth user
-- 4. Make sure the auth user ID matches the users table ID
