-- Fix Row Level Security Policies for GoGo App
-- Run this script in your Supabase SQL Editor to fix user registration issues

-- First, drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Creators can manage own videos" ON videos;
DROP POLICY IF EXISTS "Users can view own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can view own purchases" ON purchases;

-- USER POLICIES
-- Allow user registration (INSERT) - anyone can create a new user account
CREATE POLICY "Enable user registration" ON users FOR INSERT WITH CHECK (true);

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Admin users can view all users
CREATE POLICY "Admin can view all users" ON users FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Admin users can update any user
CREATE POLICY "Admin can update any user" ON users FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- VIDEO POLICIES
-- Videos are publicly readable (keep existing)
-- CREATE POLICY "Videos are publicly readable" ON videos FOR SELECT USING (true);

-- Only creators can insert their own videos
CREATE POLICY "Creators can insert own videos" ON videos FOR INSERT WITH CHECK (auth.uid() = creator_id);

-- Only creators can update their own videos
CREATE POLICY "Creators can update own videos" ON videos FOR UPDATE USING (auth.uid() = creator_id);

-- Only creators can delete their own videos
CREATE POLICY "Creators can delete own videos" ON videos FOR DELETE USING (auth.uid() = creator_id);

-- Admin users can manage all videos
CREATE POLICY "Admin can manage all videos" ON videos FOR ALL USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- SUBSCRIPTION POLICIES
-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON subscriptions FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own subscriptions
CREATE POLICY "Users can insert own subscriptions" ON subscriptions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own subscriptions
CREATE POLICY "Users can update own subscriptions" ON subscriptions FOR UPDATE USING (auth.uid() = user_id);

-- Admin users can view all subscriptions
CREATE POLICY "Admin can view all subscriptions" ON subscriptions FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- PURCHASE POLICIES
-- Users can view their own purchases
CREATE POLICY "Users can view own purchases" ON purchases FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own purchases
CREATE POLICY "Users can insert own purchases" ON purchases FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admin users can view all purchases
CREATE POLICY "Admin can view all purchases" ON purchases FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- NOTIFICATION POLICIES (if needed)
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own notifications" ON notifications FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

-- COMMENT POLICIES (if needed)
CREATE POLICY "Comments are publicly readable" ON comments FOR SELECT USING (true);
CREATE POLICY "Users can insert own comments" ON comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own comments" ON comments FOR DELETE USING (auth.uid() = user_id);

-- Admin users can manage all comments
CREATE POLICY "Admin can manage all comments" ON comments FOR ALL USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- LIKE POLICIES (if needed)
CREATE POLICY "Likes are publicly readable" ON likes FOR SELECT USING (true);
CREATE POLICY "Users can insert own likes" ON likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own likes" ON likes FOR DELETE USING (auth.uid() = user_id);

-- VIEW POLICIES (if needed)
CREATE POLICY "Views are publicly readable" ON views FOR SELECT USING (true);
CREATE POLICY "Users can insert own views" ON views FOR INSERT WITH CHECK (auth.uid() = user_id);

-- FOLLOW POLICIES (if needed)
CREATE POLICY "Follows are publicly readable" ON follows FOR SELECT USING (true);
CREATE POLICY "Users can insert own follows" ON follows FOR INSERT WITH CHECK (auth.uid() = follower_id);
CREATE POLICY "Users can delete own follows" ON follows FOR DELETE USING (auth.uid() = follower_id);

-- REPORT POLICIES (if needed)
CREATE POLICY "Users can insert reports" ON reports FOR INSERT WITH CHECK (auth.uid() = reporter_id);
CREATE POLICY "Users can view own reports" ON reports FOR SELECT USING (auth.uid() = reporter_id);

-- Admin users can view all reports
CREATE POLICY "Admin can view all reports" ON reports FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Admin users can update reports
CREATE POLICY "Admin can update reports" ON reports FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- PAYOUT POLICIES (if needed)
CREATE POLICY "Creators can view own payouts" ON payouts FOR SELECT USING (auth.uid() = creator_id);
CREATE POLICY "Creators can insert own payouts" ON payouts FOR INSERT WITH CHECK (auth.uid() = creator_id);

-- Admin users can view all payouts
CREATE POLICY "Admin can view all payouts" ON payouts FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Admin users can update payouts
CREATE POLICY "Admin can update payouts" ON payouts FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Refresh the schema
NOTIFY pgrst, 'reload schema';
