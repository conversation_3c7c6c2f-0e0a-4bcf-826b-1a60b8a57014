-- Fix "Email not confirmed" issue for existing users
-- Run this in Supabase SQL Editor

-- Step 1: Check current state of users
SELECT 
  'Before Fix' as status,
  COUNT(*) as total_users,
  COUNT(email_confirmed_at) as confirmed_users,
  COUNT(*) - COUNT(email_confirmed_at) as unconfirmed_users
FROM auth.users;

-- Step 2: Show unconfirmed users
SELECT 
  email,
  created_at,
  email_confirmed_at,
  confirmed_at
FROM auth.users 
WHERE email_confirmed_at IS NULL
ORDER BY created_at DESC;

-- Step 3: Fix all unconfirmed users
UPDATE auth.users 
SET 
  email_confirmed_at = COALESCE(email_confirmed_at, NOW()),
  confirmed_at = COALESCE(confirmed_at, NOW())
WHERE 
  email_confirmed_at IS NULL 
  OR confirmed_at IS NULL;

-- Step 4: Verify the fix
SELECT 
  'After Fix' as status,
  COUNT(*) as total_users,
  COUNT(email_confirmed_at) as confirmed_users,
  COUNT(*) - COUNT(email_confirmed_at) as unconfirmed_users
FROM auth.users;

-- Step 5: Show all users are now confirmed
SELECT 
  email,
  created_at,
  email_confirmed_at,
  confirmed_at,
  CASE 
    WHEN email_confirmed_at IS NOT NULL THEN '✅ Confirmed'
    ELSE '❌ Not Confirmed'
  END as status
FROM auth.users 
ORDER BY created_at DESC;

-- Step 6: Success message
SELECT 
  CASE 
    WHEN COUNT(*) = COUNT(email_confirmed_at) THEN '🎉 All users are now confirmed! Email confirmation issue fixed.'
    ELSE '⚠️ Some users still need confirmation. Check the results above.'
  END as result
FROM auth.users;
