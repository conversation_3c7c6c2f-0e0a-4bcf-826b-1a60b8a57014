-- MINIMAL FIX for User Registration RLS Issue
-- This script only fixes the essential user registration problem
-- Run this in your Supabase SQL Editor

-- Check what tables exist first
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Drop ALL existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admin can view all users" ON users;
DROP POLICY IF EXISTS "Admin can update any user" ON users;
DROP POLICY IF EXISTS "Enable user registration" ON users;

-- ESSENTIAL FIX: Add user registration policy
CREATE POLICY "Enable user registration" ON users
FOR INSERT
WITH CHECK (true);

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
FOR SELECT
USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
FOR UPDATE
USING (auth.uid() = id);

-- Test the fix by checking policies
SELECT 
    schemaname,
    tablename, 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'INSERT' THEN '✅ Allows user registration'
        WHEN cmd = 'SELECT' THEN '👁️ Allows reading data'
        WHEN cmd = 'UPDATE' THEN '✏️ Allows updating data'
        ELSE cmd
    END as description
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY cmd, policyname;

-- Verify RLS is enabled
SELECT 
    schemaname, 
    tablename, 
    CASE 
        WHEN rowsecurity THEN '🔒 RLS Enabled'
        ELSE '⚠️ RLS Disabled'
    END as security_status
FROM pg_tables 
WHERE tablename = 'users';

-- Success message
SELECT '🎉 User registration RLS policies have been fixed!' as status;
