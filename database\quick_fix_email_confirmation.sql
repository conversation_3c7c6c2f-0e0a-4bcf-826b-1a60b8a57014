-- QUICK FIX for "Email not confirmed" error
-- Run this in Supabase SQL Editor

-- 1. Check how many users need fixing
SELECT 
  COUNT(*) as total_users,
  COUNT(email_confirmed_at) as confirmed_users,
  COUNT(*) - COUNT(email_confirmed_at) as users_needing_fix
FROM auth.users;

-- 2. Show which users need fixing
SELECT 
  email,
  created_at,
  CASE 
    WHEN email_confirmed_at IS NULL THEN '❌ Needs Fix'
    ELSE '✅ Already Confirmed'
  END as status
FROM auth.users 
ORDER BY created_at DESC;

-- 3. FIX: Confirm all unconfirmed users
UPDATE auth.users 
SET email_confirmed_at = NOW()
WHERE email_confirmed_at IS NULL;

-- 4. Verify the fix worked
SELECT 
  COUNT(*) as total_users,
  COUNT(email_confirmed_at) as confirmed_users,
  CASE 
    WHEN COUNT(*) = COUNT(email_confirmed_at) THEN '🎉 ALL USERS FIXED!'
    ELSE '⚠️ Some users still need fixing'
  END as result
FROM auth.users;

-- 5. Show final status
SELECT 
  email,
  email_confirmed_at,
  '✅ Fixed' as status
FROM auth.users 
ORDER BY created_at DESC;
