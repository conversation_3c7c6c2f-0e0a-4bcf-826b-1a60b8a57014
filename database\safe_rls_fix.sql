-- SAFE RLS FIX - No Recursion Issues
-- This script fixes user registration without causing infinite recursion
-- Run this in your Supabase SQL Editor

-- Step 1: Clean slate - remove all existing policies on users table
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admin can view all users" ON users;
DROP POLICY IF EXISTS "Admin can update any user" ON users;
DROP POLICY IF EXISTS "Enable user registration" ON users;
DROP POLICY IF EXISTS "Creators can manage own videos" ON videos;

-- Step 2: Verify RLS is enabled on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Step 3: Create SIMPLE policies without recursion

-- 🔑 ESSENTIAL: Allow user registration (this fixes the main issue)
CREATE POLICY "Enable user registration" ON users 
FOR INSERT 
WITH CHECK (true);

-- 👤 Users can view their own profile only
CREATE POLICY "Users can view own profile" ON users 
FOR SELECT 
USING (auth.uid() = id);

-- ✏️ Users can update their own profile only
CREATE POLICY "Users can update own profile" ON users 
FOR UPDATE 
USING (auth.uid() = id);

-- Step 4: Create a simple admin function (no recursion)
-- This creates a function that checks if current user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Admin policies using the function (avoids recursion)
CREATE POLICY "Admin can view all users" ON users 
FOR SELECT 
USING (is_admin());

CREATE POLICY "Admin can update any user" ON users 
FOR UPDATE 
USING (is_admin());

-- Step 6: Fix video policies if videos table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos') THEN
    -- Drop existing video policies
    DROP POLICY IF EXISTS "Videos are publicly readable" ON videos;
    DROP POLICY IF EXISTS "Creators can manage own videos" ON videos;
    
    -- Create new video policies
    EXECUTE 'CREATE POLICY "Videos are publicly readable" ON videos FOR SELECT USING (true)';
    EXECUTE 'CREATE POLICY "Creators can insert own videos" ON videos FOR INSERT WITH CHECK (auth.uid() = creator_id)';
    EXECUTE 'CREATE POLICY "Creators can update own videos" ON videos FOR UPDATE USING (auth.uid() = creator_id)';
    EXECUTE 'CREATE POLICY "Creators can delete own videos" ON videos FOR DELETE USING (auth.uid() = creator_id)';
    EXECUTE 'CREATE POLICY "Admin can manage all videos" ON videos FOR ALL USING (is_admin())';
  END IF;
END $$;

-- Step 7: Fix subscription policies if table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
    DROP POLICY IF EXISTS "Users can view own subscriptions" ON subscriptions;
    DROP POLICY IF EXISTS "Users can insert own subscriptions" ON subscriptions;
    DROP POLICY IF EXISTS "Users can update own subscriptions" ON subscriptions;
    
    EXECUTE 'CREATE POLICY "Users can view own subscriptions" ON subscriptions FOR SELECT USING (auth.uid() = user_id)';
    EXECUTE 'CREATE POLICY "Users can insert own subscriptions" ON subscriptions FOR INSERT WITH CHECK (auth.uid() = user_id)';
    EXECUTE 'CREATE POLICY "Users can update own subscriptions" ON subscriptions FOR UPDATE USING (auth.uid() = user_id)';
    EXECUTE 'CREATE POLICY "Admin can view all subscriptions" ON subscriptions FOR SELECT USING (is_admin())';
  END IF;
END $$;

-- Step 8: Fix purchase policies if table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'purchases') THEN
    DROP POLICY IF EXISTS "Users can view own purchases" ON purchases;
    DROP POLICY IF EXISTS "Users can insert own purchases" ON purchases;
    
    EXECUTE 'CREATE POLICY "Users can view own purchases" ON purchases FOR SELECT USING (auth.uid() = user_id)';
    EXECUTE 'CREATE POLICY "Users can insert own purchases" ON purchases FOR INSERT WITH CHECK (auth.uid() = user_id)';
    EXECUTE 'CREATE POLICY "Admin can view all purchases" ON purchases FOR SELECT USING (is_admin())';
  END IF;
END $$;

-- Step 9: Verification - Check what policies were created
SELECT 
    '🎉 RLS Policies Successfully Created!' as status,
    COUNT(*) as total_policies
FROM pg_policies 
WHERE tablename = 'users';

-- Show all user policies
SELECT 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'INSERT' THEN '✅ Allows user registration'
        WHEN cmd = 'SELECT' THEN '👁️ Allows reading data'
        WHEN cmd = 'UPDATE' THEN '✏️ Allows updating data'
        ELSE cmd
    END as description
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY cmd, policyname;

-- Final success message
SELECT 
    '🚀 User registration should now work without RLS errors!' as final_status,
    'Try registering a new user in your GoGo app' as next_step;
