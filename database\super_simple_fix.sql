-- SUPER SIMPLE RLS FIX - Just Fix User Registration
-- This is the absolute minimal fix to get user registration working
-- No admin policies, no recursion, just the essentials

-- Remove all existing policies on users table
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admin can view all users" ON users;
DROP POLICY IF EXISTS "Admin can update any user" ON users;
DROP POLICY IF EXISTS "Enable user registration" ON users;

-- Ensure RLS is enabled
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 🔑 THE ESSENTIAL FIX: Allow user registration
CREATE POLICY "Enable user registration" ON users 
FOR INSERT 
WITH CHECK (true);

-- 👤 Users can view their own profile
CREATE POLICY "Users can view own profile" ON users 
FOR SELECT 
USING (auth.uid() = id);

-- ✏️ Users can update their own profile  
CREATE POLICY "Users can update own profile" ON users 
FOR UPDATE 
USING (auth.uid() = id);

-- That's it! No admin policies to avoid recursion issues

-- Verification
SELECT 
    '✅ Basic RLS policies created successfully!' as status,
    COUNT(*) as policy_count
FROM pg_policies 
WHERE tablename = 'users';

-- Show what was created
SELECT 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'INSERT' THEN '🔑 User Registration (THE FIX!)'
        WHEN cmd = 'SELECT' THEN '👁️ View Own Profile'
        WHEN cmd = 'UPDATE' THEN '✏️ Update Own Profile'
    END as purpose
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY cmd;

-- Test message
SELECT 
    '🎉 User registration should now work!' as result,
    'Try signing up a new user in your GoGo app' as test_instruction;
