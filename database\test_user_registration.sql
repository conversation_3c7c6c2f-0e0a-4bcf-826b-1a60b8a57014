-- Test User Registration after RLS Policy Fix
-- Run this script in Supabase SQL Editor to test if user registration works

-- Test 1: Check if <PERSON><PERSON> is enabled on users table
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'users';

-- Test 2: List all policies on users table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'users';

-- Test 3: Try to insert a test user (this should work now)
-- Note: Replace 'test-uuid' with an actual UUID from auth.users if testing with real auth
INSERT INTO users (
    id,
    email,
    full_name,
    username,
    role,
    subscription_status,
    phone_number
) VALUES (
    gen_random_uuid(),  -- This generates a random UUID for testing
    '<EMAIL>',
    'Test User',
    'testuser',
    'viewer',
    'free',
    '+************'
) RETURNING *;

-- Test 4: Check if the user was created
SELECT COUNT(*) as user_count FROM users WHERE email = '<EMAIL>';

-- Clean up test data
DELETE FROM users WHERE email = '<EMAIL>';

-- Test 5: Verify policies are working correctly
-- This query should show all the policies we created
SELECT 
    policyname,
    cmd,
    CASE 
        WHEN cmd = 'INSERT' THEN 'Allows user registration'
        WHEN cmd = 'SELECT' THEN 'Allows reading data'
        WHEN cmd = 'UPDATE' THEN 'Allows updating data'
        WHEN cmd = 'DELETE' THEN 'Allows deleting data'
        WHEN cmd = 'ALL' THEN 'Allows all operations'
    END as description
FROM pg_policies 
WHERE tablename = 'users'
ORDER BY cmd, policyname;
