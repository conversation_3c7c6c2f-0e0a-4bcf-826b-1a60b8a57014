# GoGo App Database Setup Guide

This guide explains how to set up both Supabase (PostgreSQL) and Firebase (Realtime Database) for your GoGo streaming app.

## Architecture Overview

- **Supabase PostgreSQL**: Main database for users, videos, subscriptions, payments, and analytics
- **Firebase Realtime Database**: Real-time notifications system

## 1. Supabase Setup (PostgreSQL)

### Step 1: Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Note down your project URL and anon key

### Step 2: Run Database Schema
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `database/supabase_schema.sql`
4. Run the SQL to create all tables and relationships

### Step 3: Configure Environment Variables
1. Copy `.env.example` to `.env`
2. Fill in your Supabase credentials:
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 2. Firebase Setup (Realtime Database)

### Step 1: Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Realtime Database
4. Set database rules to allow authenticated access

### Step 2: Get Firebase Configuration
1. Go to Project Settings > General
2. Scroll down to "Your apps" section
3. Add a web app if you haven't already
4. Copy the Firebase config object

### Step 3: Configure Environment Variables
Add your Firebase credentials to `.env`:
```env
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_DATABASE_URL=https://your_project_id-default-rtdb.firebaseio.com/
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
```

### Step 4: Firebase Database Rules
Set these rules in your Firebase Realtime Database:
```json
{
  "rules": {
    "notifications": {
      "users": {
        "$userId": {
          ".read": "$userId === auth.uid",
          ".write": "auth != null"
        }
      },
      "creators": {
        "$creatorId": {
          ".read": "$creatorId === auth.uid",
          ".write": "auth != null"
        }
      },
      "global": {
        ".read": "auth != null",
        ".write": "auth != null"
      },
      "admin": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    }
  }
}
```

## 3. Database Schema Overview

### Supabase Tables

#### Users
- Stores user profiles (viewers, creators, admin)
- Handles authentication and role management

#### Videos
- Video metadata, pricing (MWK 300 pay-per-view)
- Links to creators and categories

#### Subscriptions
- Monthly creator subscriptions (MWK 1,500/month)
- Tracks active/expired status

#### Purchases
- Pay-per-view video purchases
- Mobile money payment tracking (Airtel Money/TNM Mpamba)

#### Creator Analytics
- Revenue tracking and analytics
- Platform fee calculations (10-30% revenue share)

### Firebase Structure

```
notifications/
├── users/
│   └── {userId}/
│       └── {notificationId}: notification_data
├── creators/
│   └── {creatorId}/
│       └── {notificationId}: notification_data
├── global/
│   └── {notificationId}: notification_data
└── admin/
    └── {notificationId}: notification_data
```

## 4. Usage Examples

### Database Operations (Supabase)
```typescript
import { databaseService } from '../services/databaseService';

// Create a new video
const video = await databaseService.createVideo({
  title: 'My New Video',
  description: 'Video description',
  thumbnail_url: 'https://...',
  video_url: 'https://...',
  duration: 300,
  price: 300,
  creator_id: 'creator-uuid',
  category_id: 'category-uuid',
  is_free: false
});

// Create subscription
const subscription = await databaseService.createSubscription({
  user_id: 'user-uuid',
  creator_id: 'creator-uuid',
  status: 'active',
  price: 1500,
  start_date: new Date().toISOString(),
  end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
});
```

### Notifications (Firebase)
```typescript
import { useNotifications } from '../hooks/useNotifications';

// In a component
const { notifications, unreadCount, markAsRead } = useUserNotifications(userId);

// Send notification
await notificationService.sendUserNotification(userId, {
  type: 'payment_success',
  title: 'Payment Successful',
  message: 'Your payment was processed successfully',
  data: { amount: 300 }
});
```

## 5. Key Features

### Payment Integration
- Supports Airtel Money and TNM Mpamba
- Tracks transaction IDs and status
- Automatic notification on payment success/failure

### Revenue Sharing
- 10-30% platform fee configurable
- Monthly analytics for creators
- Withdrawal request system

### Real-time Features
- Instant notifications for new videos
- Payment confirmations
- Subscription updates
- Admin announcements

## 6. Security

### Supabase RLS (Row Level Security)
- Users can only access their own data
- Creators can only manage their own content
- Admin has elevated permissions

### Firebase Security Rules
- Users can only read their own notifications
- Authenticated users can write notifications
- Proper validation for data structure

## 7. Next Steps

1. Set up your Supabase and Firebase projects
2. Configure environment variables
3. Run the database schema
4. Test the notification system
5. Implement payment processing
6. Add user authentication

For more detailed implementation examples, check the service files:
- `src/services/databaseService.ts`
- `src/services/notificationService.ts`
- `src/hooks/useNotifications.ts`
