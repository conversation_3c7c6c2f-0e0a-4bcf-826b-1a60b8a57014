# 🚀 GoGo App Setup Guide

Complete step-by-step guide to set up Supabase and Firebase for your GoGo streaming app.

## 📋 Prerequisites

- Node.js (v16 or higher)
- Expo CLI installed (`npm install -g @expo/cli`)
- Google account (for Firebase)
- GitHub account (for Supabase - recommended)

## 🗄️ Part 1: Supabase Setup (PostgreSQL Database)

### Step 1: Create Supabase Project

1. **Visit Supabase**
   - Go to [supabase.com](https://supabase.com)
   - Click "Start your project"
   - Sign up/Sign in (GitHub recommended)

2. **Create New Project**
   - Click "New Project"
   - **Project Name**: `gogo-streaming-app`
   - **Database Password**: Generate strong password (SAVE THIS!)
   - **Region**: Choose closest to your users
   - Click "Create new project"
   - ⏳ Wait 2-3 minutes for setup

### Step 2: Get Supabase Credentials

1. **Navigate to API Settings**
   - Click gear icon (⚙️) in sidebar
   - Go to "API" section

2. **Copy These Values**
   ```
   Project URL: https://your-project-id.supabase.co
   Anon Public Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### Step 3: Set Up Database Schema

1. **Open SQL Editor**
   - Click "SQL Editor" in sidebar
   - Click "New Query"

2. **Run Database Schema**
   - Open `database/supabase_schema.sql` in your project
   - Copy ALL content (Ctrl+A, Ctrl+C)
   - Paste in SQL editor
   - Click "Run" (▶️ button)

3. **Verify Tables Created**
   - Go to "Table Editor" in sidebar
   - You should see these tables:
     - ✅ users
     - ✅ videos  
     - ✅ categories
     - ✅ subscriptions
     - ✅ purchases
     - ✅ video_progress
     - ✅ playlists
     - ✅ creator_analytics
     - ✅ withdrawal_requests

## 🔥 Part 2: Firebase Setup (Real-time Notifications)

### Step 1: Create Firebase Project

1. **Visit Firebase Console**
   - Go to [console.firebase.google.com](https://console.firebase.google.com)
   - Sign in with Google account

2. **Create New Project**
   - Click "Create a project"
   - **Project Name**: `gogo-streaming-app`
   - **Enable Google Analytics**: ✅ Yes
   - **Analytics Account**: Default or create new
   - Click "Create project"
   - ⏳ Wait for setup

### Step 2: Enable Realtime Database

1. **Navigate to Realtime Database**
   - In Firebase console sidebar, click "Realtime Database"
   - Click "Create Database"

2. **Configure Database**
   - **Location**: Choose region closest to users
   - **Security Rules**: "Start in test mode"
   - Click "Enable"

### Step 3: Configure Security Rules

1. **Go to Rules Tab**
   - In Realtime Database, click "Rules" tab

2. **Replace Default Rules**
   - Delete existing content
   - Paste this configuration:

```json
{
  "rules": {
    "notifications": {
      "users": {
        "$userId": {
          ".read": "$userId === auth.uid",
          ".write": "auth != null"
        }
      },
      "creators": {
        "$creatorId": {
          ".read": "$creatorId === auth.uid", 
          ".write": "auth != null"
        }
      },
      "global": {
        ".read": "auth != null",
        ".write": "auth != null"
      },
      "admin": {
        ".read": "auth != null",
        ".write": "auth != null"
      }
    }
  }
}
```

3. **Publish Rules**
   - Click "Publish"

### Step 4: Get Firebase Configuration

1. **Add Web App**
   - Go to "Project Overview"
   - Click web icon `</>`
   - **App nickname**: `gogo-web-app`
   - **Don't check** "Set up Firebase Hosting"
   - Click "Register app"

2. **Copy Configuration Object**
   - You'll see something like:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "your-project.firebaseapp.com",
  databaseURL: "https://your-project-default-rtdb.firebaseio.com/",
  projectId: "your-project",
  storageBucket: "your-project.appspot.com", 
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abc123"
};
```

## ⚙️ Part 3: Configure Environment Variables

### Step 1: Update .env File

1. **Open `.env` file** in your project root

2. **Replace placeholder values** with your actual credentials:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_from_supabase

# Firebase Configuration  
EXPO_PUBLIC_FIREBASE_API_KEY=your_actual_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
```

### Step 2: Test Configuration

1. **Start the app**:
```bash
npm start
```

2. **Check for errors**:
   - No "Database not configured" errors
   - App should load without crashing
   - Check Expo console for any Firebase/Supabase errors

## 🧪 Part 4: Test Your Setup

### Test Supabase Connection

1. **Try to register a new user**
   - Go to registration screen
   - Fill in details
   - Should create user in Supabase

2. **Check Supabase Dashboard**
   - Go to "Table Editor" > "users"
   - Your new user should appear

### Test Firebase Notifications

1. **Check Firebase Console**
   - Go to Realtime Database
   - You should see data structure being created

## 🎯 Part 5: Next Steps

Once setup is complete, you can:

1. **Test User Registration/Login**
2. **Upload test videos** (implement file upload)
3. **Set up payment processing** (Stripe)
4. **Configure mobile money** (Airtel Money/TNM Mpamba)
5. **Test notifications** between users

## 🚨 Troubleshooting

### Common Issues

**"Database not configured" error**
- ✅ Check `.env` file has correct values
- ✅ Restart Expo development server
- ✅ Clear Expo cache: `expo start -c`

**Firebase connection issues**
- ✅ Verify Firebase project is active
- ✅ Check database rules are published
- ✅ Ensure all Firebase config values are correct

**Supabase authentication issues**
- ✅ Verify RLS policies are set up
- ✅ Check API key is the "anon" key, not service key
- ✅ Ensure project URL is correct

## 📞 Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure both Supabase and Firebase projects are active
4. Check that the database schema was applied successfully

Your GoGo app should now be connected to both databases! 🎉
