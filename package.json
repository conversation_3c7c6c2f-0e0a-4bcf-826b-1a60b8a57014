{"name": "gogo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "verify-setup": "node scripts/verify-setup.js", "setup-check": "npm run verify-setup"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "^4.5.7", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "7.3.14", "@react-navigation/native": "^7.1.6", "@reduxjs/toolkit": "2.8.2", "@stripe/stripe-react-native": "0.45.0", "@supabase/supabase-js": "2.50.0", "expo": "~53.0.10", "expo-av": "^15.1.5", "expo-dev-client": "~5.2.0", "expo-document-picker": "^13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.5", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.3", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "firebase": "^11.10.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-video": "^6.14.1", "react-native-web": "~0.20.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.6", "react-test-renderer": "19.0.0", "typescript": "~5.8.3", "webpack": "^5.99.9"}, "private": true}