#!/usr/bin/env node

/**
 * GoGo App Setup Verification Script
 * 
 * This script checks if your environment variables are properly configured
 * for Supabase and Firebase integration.
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 GoGo App Setup Verification\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found!');
  console.log('📝 Please copy .env.example to .env and fill in your credentials');
  process.exit(1);
}

// Read .env file
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));

// Parse environment variables
const envVars = {};
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});

console.log('📋 Checking Environment Variables...\n');

// Required Supabase variables
const supabaseVars = {
  'EXPO_PUBLIC_SUPABASE_URL': 'Supabase Project URL',
  'EXPO_PUBLIC_SUPABASE_ANON_KEY': 'Supabase Anonymous Key'
};

// Required Firebase variables
const firebaseVars = {
  'EXPO_PUBLIC_FIREBASE_API_KEY': 'Firebase API Key',
  'EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN': 'Firebase Auth Domain',
  'EXPO_PUBLIC_FIREBASE_DATABASE_URL': 'Firebase Database URL',
  'EXPO_PUBLIC_FIREBASE_PROJECT_ID': 'Firebase Project ID',
  'EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET': 'Firebase Storage Bucket',
  'EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID': 'Firebase Messaging Sender ID',
  'EXPO_PUBLIC_FIREBASE_APP_ID': 'Firebase App ID'
};

let allValid = true;

// Check Supabase configuration
console.log('🗄️  Supabase Configuration:');
Object.entries(supabaseVars).forEach(([key, description]) => {
  const value = envVars[key];
  if (!value || value.includes('your-') || value.includes('placeholder')) {
    console.log(`   ❌ ${description}: Not configured`);
    allValid = false;
  } else {
    console.log(`   ✅ ${description}: Configured`);
  }
});

console.log('\n🔥 Firebase Configuration:');
Object.entries(firebaseVars).forEach(([key, description]) => {
  const value = envVars[key];
  if (!value || value.includes('your-') || value.includes('placeholder')) {
    console.log(`   ❌ ${description}: Not configured`);
    allValid = false;
  } else {
    console.log(`   ✅ ${description}: Configured`);
  }
});

// Check for common configuration issues
console.log('\n🔍 Configuration Validation:');

// Supabase URL format check
const supabaseUrl = envVars['EXPO_PUBLIC_SUPABASE_URL'];
if (supabaseUrl && !supabaseUrl.includes('supabase.co')) {
  console.log('   ⚠️  Supabase URL format looks incorrect');
  console.log('      Expected format: https://your-project-id.supabase.co');
  allValid = false;
} else if (supabaseUrl) {
  console.log('   ✅ Supabase URL format looks correct');
}

// Firebase Database URL format check
const firebaseDbUrl = envVars['EXPO_PUBLIC_FIREBASE_DATABASE_URL'];
if (firebaseDbUrl && !firebaseDbUrl.includes('firebaseio.com')) {
  console.log('   ⚠️  Firebase Database URL format looks incorrect');
  console.log('      Expected format: https://your-project-id-default-rtdb.firebaseio.com/');
  allValid = false;
} else if (firebaseDbUrl) {
  console.log('   ✅ Firebase Database URL format looks correct');
}

// Firebase Auth Domain format check
const firebaseAuthDomain = envVars['EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN'];
if (firebaseAuthDomain && !firebaseAuthDomain.includes('firebaseapp.com')) {
  console.log('   ⚠️  Firebase Auth Domain format looks incorrect');
  console.log('      Expected format: your-project-id.firebaseapp.com');
  allValid = false;
} else if (firebaseAuthDomain) {
  console.log('   ✅ Firebase Auth Domain format looks correct');
}

// Check if database schema file exists
const schemaPath = path.join(process.cwd(), 'database', 'supabase_schema.sql');
if (!fs.existsSync(schemaPath)) {
  console.log('\n   ⚠️  Database schema file not found at database/supabase_schema.sql');
} else {
  console.log('\n   ✅ Database schema file found');
}

// Final result
console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 Configuration looks good!');
  console.log('\n📋 Next Steps:');
  console.log('1. Make sure you\'ve run the database schema in Supabase');
  console.log('2. Verify Firebase Realtime Database is enabled');
  console.log('3. Start your app: npm start');
  console.log('4. Test the database connections in the app');
} else {
  console.log('❌ Configuration issues found!');
  console.log('\n📋 To fix:');
  console.log('1. Update your .env file with correct credentials');
  console.log('2. Follow the setup guide: docs/setup-guide.md');
  console.log('3. Restart your development server after making changes');
}
console.log('='.repeat(50));

process.exit(allValid ? 0 : 1);
