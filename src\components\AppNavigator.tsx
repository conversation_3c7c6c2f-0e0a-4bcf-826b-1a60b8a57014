import React, { useEffect, useState } from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store';
import { getCurrentUser } from '../store/slices/authSlice';
import { GoGoColors } from '../../constants/Colors';
import AuthScreen from '../screens/auth/AuthScreen';
import MainTabNavigator from './MainTabNavigator';
import VideoPlayerScreen from '../screens/main/VideoPlayerScreen';
import CreatorDashboard from '../screens/creator/CreatorDashboard';
import AdminDashboard from '../screens/admin/AdminDashboard';
import { Video } from '../types';

export default function AppNavigator() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading, user } = useAppSelector((state) => state.auth);

  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null);

  useEffect(() => {
    // Check for existing session on app start
    dispatch(getCurrentUser());
  }, [dispatch]);

  const handleVideoPress = (video: Video) => {
    setCurrentVideoId(video.id);
  };

  const handleCloseVideo = () => {
    setCurrentVideoId(null);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        {/* You can add a loading spinner here */}
      </View>
    );
  }

  if (currentVideoId) {
    return (
      <VideoPlayerScreen
        videoId={currentVideoId}
        onClose={handleCloseVideo}
      />
    );
  }

  // Function to render the appropriate dashboard based on user role
  const renderDashboard = () => {
    if (!user) return <MainTabNavigator onVideoPress={handleVideoPress} />;

    // Check user role and render appropriate dashboard
    switch (user.user_type) {
      case 'admin':
        return <AdminDashboard />;
      case 'creator':
        return <CreatorDashboard />;
      case 'user':
      default:
        return <MainTabNavigator onVideoPress={handleVideoPress} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {isAuthenticated ? renderDashboard() : <AuthScreen />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
