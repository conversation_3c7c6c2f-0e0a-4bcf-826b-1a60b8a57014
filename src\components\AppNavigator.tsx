import React, { useEffect, useState } from 'react';
import { View, StyleSheet, StatusBar, ActivityIndicator } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store';
import { getCurrentUser } from '../store/slices/authSlice';
import { GoGoColors } from '../../constants/Colors';
import AuthScreen from '../screens/auth/AuthScreen';
import MainTabNavigator from './MainTabNavigator';
import VideoPlayerScreen from '../screens/main/VideoPlayerScreen';
import CreatorDashboard from '../screens/creator/CreatorDashboard';
import AdminDashboard from '../screens/admin/AdminDashboard';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Video } from '../types';

export default function AppNavigator() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading, user } = useAppSelector((state) => state.auth);

  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    // Check for existing session on app start
    const initializeAuth = async () => {
      try {
        await dispatch(getCurrentUser());
      } catch (error) {
        console.log('Auth initialization error:', error);
      } finally {
        // Reduce delay to minimize white screen
        setTimeout(() => {
          setIsInitializing(false);
        }, 100);
      }
    };

    initializeAuth();
  }, [dispatch]);

  const handleVideoPress = (video: Video) => {
    setCurrentVideoId(video.id);
  };

  const handleCloseVideo = () => {
    setCurrentVideoId(null);
  };

  // Simple Loading Screen - Twitter Style
  const LoadingScreen = () => (
    <View style={styles.loadingContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <ActivityIndicator size="large" color="#1DA1F2" />
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        {/* You can add a loading spinner here */}
      </View>
    );
  }

  if (currentVideoId) {
    return (
      <VideoPlayerScreen
        videoId={currentVideoId}
        onClose={handleCloseVideo}
      />
    );
  }

  // Function to render the appropriate dashboard based on user role
  const renderDashboard = () => {
    if (!user) return <MainTabNavigator onVideoPress={handleVideoPress} />;

    // Check user role and render appropriate dashboard
    // Note: Database uses 'role' field, but types might use 'user_type'
    const userRole = user.role || user.user_type;

    // Debug logging
    console.log('🔍 User Role Debug:', {
      userRole,
      userRoleField: user.role,
      userTypeField: user.user_type,
      userEmail: user.email,
      fullUser: user
    });

    if (userRole === 'admin') {
      console.log('✅ Rendering AdminDashboard');
      return <AdminDashboard />;
    } else if (userRole === 'creator') {
      console.log('✅ Rendering CreatorDashboard');
      return <CreatorDashboard />;
    } else {
      console.log('✅ Rendering MainTabNavigator (default)');
      return <MainTabNavigator onVideoPress={handleVideoPress} />;
    }
  };

  // Debug logging for auth state
  console.log('🔍 AppNavigator State:', {
    isInitializing,
    isLoading,
    isAuthenticated,
    hasUser: !!user,
    userRole: user?.role
  });

  // Show loading screen during initialization, auth loading, or when user state is transitioning
  if (isInitializing || isLoading || (isAuthenticated && !user)) {
    console.log('📱 Showing loading screen');
    return <LoadingScreen />;
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {isAuthenticated ? (
        <Animated.View
          key="authenticated"
          style={styles.container}
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(100)}
        >
          {renderDashboard()}
        </Animated.View>
      ) : (
        <Animated.View
          key="unauthenticated"
          style={styles.container}
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(100)}
        >
          <AuthScreen />
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
