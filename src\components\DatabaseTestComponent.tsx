import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { supabase } from '../config/supabase';
import { database } from '../config/firebase';
import { ref, set, onValue, off } from 'firebase/database';

interface DatabaseTestComponentProps {
  onClose?: () => void;
}

const DatabaseTestComponent: React.FC<DatabaseTestComponentProps> = ({ onClose }) => {
  const [supabaseStatus, setSupabaseStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [firebaseStatus, setFirebaseStatus] = useState<'testing' | 'connected' | 'error'>('testing');
  const [supabaseError, setSupabaseError] = useState<string>('');
  const [firebaseError, setFirebaseError] = useState<string>('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testSupabaseConnection = async () => {
    try {
      addTestResult('Testing Supabase connection...');
      
      if (!supabase) {
        throw new Error('Supabase client not initialized. Check your environment variables.');
      }

      // Test basic connection
      const { data, error } = await supabase
        .from('categories')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      addTestResult('✅ Supabase connection successful!');
      addTestResult(`✅ Categories table accessible`);
      setSupabaseStatus('connected');
      
    } catch (error: any) {
      console.error('Supabase test error:', error);
      setSupabaseError(error.message);
      setSupabaseStatus('error');
      addTestResult(`❌ Supabase error: ${error.message}`);
    }
  };

  const testFirebaseConnection = async () => {
    try {
      addTestResult('Testing Firebase connection...');
      
      if (!database) {
        throw new Error('Firebase database not initialized. Check your environment variables.');
      }

      // Test write operation
      const testRef = ref(database, 'test/connection');
      await set(testRef, {
        timestamp: Date.now(),
        message: 'Connection test successful'
      });

      addTestResult('✅ Firebase write test successful!');

      // Test read operation
      const listener = onValue(testRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          addTestResult('✅ Firebase read test successful!');
          setFirebaseStatus('connected');
        }
        off(testRef, 'value', listener);
      }, (error) => {
        throw error;
      });

    } catch (error: any) {
      console.error('Firebase test error:', error);
      setFirebaseError(error.message);
      setFirebaseStatus('error');
      addTestResult(`❌ Firebase error: ${error.message}`);
    }
  };

  const testNotificationSystem = async () => {
    try {
      addTestResult('Testing notification system...');
      
      const testNotificationRef = ref(database, 'notifications/global');
      const testNotification = {
        id: `test_${Date.now()}`,
        type: 'system_announcement',
        title: 'Database Test',
        message: 'This is a test notification to verify the system is working.',
        timestamp: Date.now(),
        read: false,
      };

      await set(testNotificationRef, testNotification);
      addTestResult('✅ Test notification sent successfully!');
      
    } catch (error: any) {
      addTestResult(`❌ Notification test error: ${error.message}`);
    }
  };

  const runAllTests = async () => {
    setTestResults([]);
    setSupabaseStatus('testing');
    setFirebaseStatus('testing');
    setSupabaseError('');
    setFirebaseError('');

    addTestResult('🚀 Starting database connection tests...');
    
    await testSupabaseConnection();
    await testFirebaseConnection();
    
    if (supabaseStatus === 'connected' && firebaseStatus === 'connected') {
      await testNotificationSystem();
      addTestResult('🎉 All tests completed successfully!');
    }
  };

  useEffect(() => {
    runAllTests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return '#4CAF50';
      case 'error': return '#F44336';
      case 'testing': return '#FF9800';
      default: return '#757575';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '✅ Connected';
      case 'error': return '❌ Error';
      case 'testing': return '🔄 Testing...';
      default: return '⏳ Waiting';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Database Connection Test</Text>
      
      {/* Status Cards */}
      <View style={styles.statusContainer}>
        <View style={styles.statusCard}>
          <Text style={styles.statusTitle}>Supabase (PostgreSQL)</Text>
          <Text style={[styles.statusText, { color: getStatusColor(supabaseStatus) }]}>
            {getStatusText(supabaseStatus)}
          </Text>
          {supabaseError ? <Text style={styles.errorText}>{supabaseError}</Text> : null}
        </View>

        <View style={styles.statusCard}>
          <Text style={styles.statusTitle}>Firebase (Realtime DB)</Text>
          <Text style={[styles.statusText, { color: getStatusColor(firebaseStatus) }]}>
            {getStatusText(firebaseStatus)}
          </Text>
          {firebaseError ? <Text style={styles.errorText}>{firebaseError}</Text> : null}
        </View>
      </View>

      {/* Test Results */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        <ScrollView style={styles.resultsScroll}>
          {testResults.map((result, index) => (
            <Text key={index} style={styles.resultText}>
              {result}
            </Text>
          ))}
        </ScrollView>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.testButton} onPress={runAllTests}>
          <Text style={styles.testButtonText}>Run Tests Again</Text>
        </TouchableOpacity>
        
        {onClose && (
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Setup Instructions */}
      {(supabaseStatus === 'error' || firebaseStatus === 'error') && (
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Setup Instructions:</Text>
          <Text style={styles.instructionsText}>
            1. Check your .env file has correct credentials{'\n'}
            2. Restart Expo development server{'\n'}
            3. Verify Supabase project is active{'\n'}
            4. Verify Firebase Realtime Database is enabled{'\n'}
            5. Check docs/setup-guide.md for detailed instructions
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statusCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
    marginTop: 5,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultsScroll: {
    flex: 1,
  },
  resultText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  testButton: {
    flex: 1,
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    marginRight: 10,
  },
  testButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    flex: 1,
    backgroundColor: '#757575',
    padding: 15,
    borderRadius: 10,
    marginLeft: 10,
  },
  closeButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E65100',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    color: '#BF360C',
    lineHeight: 20,
  },
});

export { DatabaseTestComponent };
export default DatabaseTestComponent;
