import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { databaseService } from '../services/databaseService';
import { notificationService } from '../services/notificationService';

interface VideoUploadExampleProps {
  creatorId: string;
  onUploadComplete?: (videoId: string) => void;
}

export const VideoUploadExample: React.FC<VideoUploadExampleProps> = ({
  creatorId,
  onUploadComplete,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('300'); // Default MWK 300
  const [isFree, setIsFree] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleVideoUpload = async () => {
    if (!title.trim() || !description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setUploading(true);

    try {
      // In a real app, you would upload the video file first
      // For this example, we'll use placeholder URLs
      const videoData = {
        title: title.trim(),
        description: description.trim(),
        thumbnail_url: 'https://example.com/thumbnail.jpg', // Replace with actual upload
        video_url: 'https://example.com/video.mp4', // Replace with actual upload
        duration: 300, // Replace with actual video duration
        price: isFree ? 0 : parseFloat(price),
        creator_id: creatorId,
        category_id: 'default-category-id', // Replace with selected category
        is_free: isFree,
      };

      // 1. Save video to Supabase (PostgreSQL)
      const newVideo = await databaseService.createVideo(videoData);

      // 2. Send notification via Firebase (this is automatically done in createVideo)
      // But you can also send custom notifications:
      await notificationService.sendGlobalNotification({
        type: 'new_video',
        title: 'New Video Available!',
        message: `"${title}" has been uploaded and is now available`,
        data: {
          videoId: newVideo.id,
          creatorId: creatorId,
          price: videoData.price,
          isFree: isFree,
        },
      });

      // 3. Notify the creator about successful upload
      await notificationService.sendCreatorNotification(creatorId, {
        type: 'system_announcement',
        title: 'Video Upload Successful',
        message: `Your video "${title}" has been uploaded successfully and is now live!`,
        data: {
          videoId: newVideo.id,
        },
      });

      Alert.alert(
        'Success',
        'Your video has been uploaded successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              // Reset form
              setTitle('');
              setDescription('');
              setPrice('300');
              setIsFree(false);
              
              // Callback to parent component
              onUploadComplete?.(newVideo.id);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Video upload error:', error);
      Alert.alert(
        'Upload Failed',
        'There was an error uploading your video. Please try again.'
      );
    } finally {
      setUploading(false);
    }
  };

  const handleSubscriptionExample = async (userId: string) => {
    try {
      // Example: Create a subscription (MWK 1,500/month)
      const subscriptionData = {
        user_id: userId,
        creator_id: creatorId,
        status: 'active' as const,
        price: 1500,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        payment_method: 'airtel_money' as const,
        transaction_id: `TXN_${Date.now()}`,
      };

      // 1. Save subscription to Supabase
      const subscription = await databaseService.createSubscription(subscriptionData);

      // 2. Send notifications (automatically done in createSubscription)
      console.log('Subscription created:', subscription);
    } catch (error) {
      console.error('Subscription error:', error);
    }
  };

  const handlePurchaseExample = async (userId: string, videoId: string) => {
    try {
      // Example: Create a video purchase (MWK 300)
      const purchaseData = {
        user_id: userId,
        video_id: videoId,
        amount: 300,
        payment_method: 'tnm_mpamba' as const,
        transaction_id: `PUR_${Date.now()}`,
        status: 'pending' as const,
      };

      // 1. Create purchase record in Supabase
      const purchase = await databaseService.createPurchase(purchaseData);

      // 2. Simulate payment processing
      setTimeout(async () => {
        // Update purchase status to completed
        await databaseService.updatePurchaseStatus(purchase.id, 'completed');
        
        // Send success notification (automatically done in updatePurchaseStatus)
        console.log('Purchase completed:', purchase);
      }, 2000);
    } catch (error) {
      console.error('Purchase error:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Upload New Video</Text>
      
      <View style={styles.form}>
        <TextInput
          style={styles.input}
          placeholder="Video Title"
          value={title}
          onChangeText={setTitle}
          editable={!uploading}
        />
        
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Video Description"
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={4}
          editable={!uploading}
        />
        
        <View style={styles.priceContainer}>
          <TouchableOpacity
            style={[styles.checkbox, isFree && styles.checkboxChecked]}
            onPress={() => setIsFree(!isFree)}
            disabled={uploading}
          >
            {isFree && <Text style={styles.checkmark}>✓</Text>}
          </TouchableOpacity>
          <Text style={styles.checkboxLabel}>Free Video</Text>
        </View>
        
        {!isFree && (
          <TextInput
            style={styles.input}
            placeholder="Price (MWK)"
            value={price}
            onChangeText={setPrice}
            keyboardType="numeric"
            editable={!uploading}
          />
        )}
        
        <TouchableOpacity
          style={[styles.uploadButton, uploading && styles.uploadButtonDisabled]}
          onPress={handleVideoUpload}
          disabled={uploading}
        >
          {uploading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.uploadButtonText}>Upload Video</Text>
          )}
        </TouchableOpacity>
      </View>
      
      <View style={styles.exampleSection}>
        <Text style={styles.exampleTitle}>Database Integration Examples:</Text>
        <Text style={styles.exampleText}>
          • Video upload saves to Supabase PostgreSQL{'\n'}
          • Notifications sent via Firebase Realtime DB{'\n'}
          • Subscriptions: MWK 1,500/month{'\n'}
          • Pay-per-view: MWK 300{'\n'}
          • Mobile money: Airtel Money & TNM Mpamba{'\n'}
          • Real-time notifications for all users
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  form: {
    marginBottom: 30,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#333',
  },
  uploadButton: {
    backgroundColor: '#FFD700', // Gold color as per user preference
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  uploadButtonDisabled: {
    backgroundColor: '#ccc',
  },
  uploadButtonText: {
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
  },
  exampleSection: {
    backgroundColor: '#f5f5f5',
    padding: 16,
    borderRadius: 8,
  },
  exampleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  exampleText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default VideoUploadExample;
