import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY || 'placeholder-api-key',
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || 'placeholder.firebaseapp.com',
  databaseURL: process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL || 'https://placeholder-default-rtdb.firebaseio.com/',
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || 'placeholder-project',
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || 'placeholder.appspot.com',
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '123456789',
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID || 'placeholder-app-id'
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Realtime Database and get a reference to the service
export const database = getDatabase(app);

// Notification paths in Firebase Realtime Database
export const NOTIFICATION_PATHS = {
  USER_NOTIFICATIONS: (userId: string) => `notifications/users/${userId}`,
  GLOBAL_NOTIFICATIONS: 'notifications/global',
  CREATOR_NOTIFICATIONS: (creatorId: string) => `notifications/creators/${creatorId}`,
  ADMIN_NOTIFICATIONS: 'notifications/admin',
} as const;

// Notification types
export const NOTIFICATION_TYPES = {
  NEW_VIDEO: 'new_video',
  SUBSCRIPTION_CREATED: 'subscription_created',
  SUBSCRIPTION_EXPIRED: 'subscription_expired',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAILED: 'payment_failed',
  VIDEO_PURCHASED: 'video_purchased',
  CREATOR_APPROVED: 'creator_approved',
  CREATOR_REJECTED: 'creator_rejected',
  SYSTEM_ANNOUNCEMENT: 'system_announcement',
} as const;

export default app;
