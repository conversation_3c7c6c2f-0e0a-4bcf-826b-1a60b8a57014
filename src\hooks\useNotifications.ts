import { useState, useEffect, useCallback } from 'react';
import { notificationService, Notification } from '../services/notificationService';

interface UseNotificationsProps {
  userId?: string;
  creatorId?: string;
  isAdmin?: boolean;
  includeGlobal?: boolean;
}

export const useNotifications = ({
  userId,
  creatorId,
  isAdmin = false,
  includeGlobal = true,
}: UseNotificationsProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Combine notifications from different sources
  const combineNotifications = useCallback((
    userNotifs: Notification[] = [],
    creatorNotifs: Notification[] = [],
    globalNotifs: Notification[] = [],
    adminNotifs: Notification[] = []
  ) => {
    const allNotifications = [
      ...userNotifs,
      ...creatorNotifs,
      ...globalNotifs,
      ...adminNotifs,
    ];

    // Sort by timestamp (most recent first) and remove duplicates
    const uniqueNotifications = allNotifications
      .filter((notif, index, self) => 
        index === self.findIndex(n => n.id === notif.id)
      )
      .sort((a, b) => b.timestamp - a.timestamp);

    return uniqueNotifications;
  }, []);

  useEffect(() => {
    if (!userId && !creatorId && !isAdmin) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const listeners: (() => void)[] = [];
    let userNotifications: Notification[] = [];
    let creatorNotifications: Notification[] = [];
    let globalNotifications: Notification[] = [];
    let adminNotifications: Notification[] = [];

    const updateCombinedNotifications = () => {
      const combined = combineNotifications(
        userNotifications,
        creatorNotifications,
        globalNotifications,
        adminNotifications
      );
      setNotifications(combined);
      setLoading(false);
    };

    try {
      // Listen to user notifications
      if (userId) {
        const userListener = notificationService.listenToUserNotifications(
          userId,
          (notifs) => {
            userNotifications = notifs;
            updateCombinedNotifications();
          }
        );
        listeners.push(() => notificationService.stopListening(`user_${userId}`));
      }

      // Listen to creator notifications
      if (creatorId) {
        const creatorListener = notificationService.listenToCreatorNotifications(
          creatorId,
          (notifs) => {
            creatorNotifications = notifs;
            updateCombinedNotifications();
          }
        );
        listeners.push(() => notificationService.stopListening(`creator_${creatorId}`));
      }

      // Listen to global notifications
      if (includeGlobal) {
        const globalListener = notificationService.listenToGlobalNotifications(
          (notifs) => {
            globalNotifications = notifs;
            updateCombinedNotifications();
          }
        );
        listeners.push(() => notificationService.stopListening('global'));
      }

      // Listen to admin notifications
      if (isAdmin) {
        const adminListener = notificationService.listenToAdminNotifications(
          (notifs) => {
            adminNotifications = notifs;
            updateCombinedNotifications();
          }
        );
        listeners.push(() => notificationService.stopListening('admin'));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
      setLoading(false);
    }

    // Cleanup function
    return () => {
      listeners.forEach(cleanup => cleanup());
    };
  }, [userId, creatorId, isAdmin, includeGlobal, combineNotifications]);

  // Get unread notification count
  const unreadCount = notifications.filter(notif => !notif.read).length;

  // Mark notification as read (this would typically update the database)
  const markAsRead = useCallback(async (notificationId: string) => {
    // Note: In a real implementation, you would update the notification status in the database
    // For now, we'll just update the local state
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    // Note: In a real implementation, you would batch update all notifications in the database
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  }, []);

  // Send a notification (for admin/creator use)
  const sendNotification = useCallback(async (
    type: 'user' | 'creator' | 'global' | 'admin',
    targetId: string | null,
    notification: Omit<Notification, 'id' | 'timestamp' | 'read'>
  ) => {
    try {
      switch (type) {
        case 'user':
          if (targetId) {
            await notificationService.sendUserNotification(targetId, notification);
          }
          break;
        case 'creator':
          if (targetId) {
            await notificationService.sendCreatorNotification(targetId, notification);
          }
          break;
        case 'global':
          await notificationService.sendGlobalNotification(notification);
          break;
        case 'admin':
          await notificationService.sendAdminNotification(notification);
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send notification');
      throw err;
    }
  }, []);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    sendNotification,
  };
};

// Hook specifically for user notifications
export const useUserNotifications = (userId: string) => {
  return useNotifications({ userId, includeGlobal: true });
};

// Hook specifically for creator notifications
export const useCreatorNotifications = (creatorId: string) => {
  return useNotifications({ creatorId, includeGlobal: true });
};

// Hook specifically for admin notifications
export const useAdminNotifications = () => {
  return useNotifications({ isAdmin: true, includeGlobal: true });
};
