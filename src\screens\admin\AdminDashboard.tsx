import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  StatusBar,
  RefreshControl,
  FlatList,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  FadeIn,
  SlideInDown,
  SlideInRight,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';
import { formatViewCount } from '../../utils/formatters';
import PlatformSettingsScreen from './PlatformSettingsScreen';
import ContentModerationScreen from './ContentModerationScreen';
import UserManagementScreen from './UserManagementScreen';
import FinancialDashboardScreen from './FinancialDashboardScreen';
import SystemHealthScreen from './SystemHealthScreen';
import PlatformAnalyticsScreen from './PlatformAnalyticsScreen';
import { signOut } from '../../store/slices/authSlice';

const { width, height } = Dimensions.get('window');

// Admin Stats Card Component
interface AdminStatsCardProps {
  icon: string;
  value: string;
  label: string;
  color: string;
  index: number;
  trend?: string;
  onPress?: () => void;
}

function AdminStatsCard({ icon, value, label, color, index, trend, onPress }: AdminStatsCardProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    if (onPress) {
      scale.value = withSpring(0.95);
      hapticFeedback.light();
    }
  };

  const handlePressOut = () => {
    if (onPress) {
      scale.value = withSpring(1);
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View style={[styles.statsCard, animatedStyle]} entering={FadeIn.delay(index * 150)}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
        style={styles.statsCardTouchable}
      >
        <LinearGradient
          colors={[color + '15', color + '05']}
          style={styles.statsGradient}
        >
          <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon as any} size={24} color={color} />
          </View>
          <Text style={styles.statsValue}>{value}</Text>
          <Text style={styles.statsLabel}>{label}</Text>
          {trend && (
            <View style={styles.trendContainer}>
              <Ionicons 
                name={trend.startsWith('+') ? 'trending-up' : 'trending-down'} 
                size={12} 
                color={trend.startsWith('+') ? GoGoColors.success : GoGoColors.error} 
              />
              <Text style={[
                styles.trendText, 
                { color: trend.startsWith('+') ? GoGoColors.success : GoGoColors.error }
              ]}>
                {trend}
              </Text>
            </View>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Admin Action Card Component
interface AdminActionProps {
  icon: string;
  title: string;
  subtitle: string;
  color: string;
  onPress: () => void;
  index: number;
  badge?: string;
}

function AdminActionCard({ icon, title, subtitle, color, onPress, index, badge }: AdminActionProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View 
      style={[styles.actionCard, animatedStyle]} 
      entering={SlideInRight.delay(index * 100)}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        style={styles.actionTouchable}
      >
        <View style={styles.actionContent}>
          <View style={[styles.actionIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon as any} size={24} color={color} />
          </View>
          <View style={styles.actionText}>
            <Text style={styles.actionTitle}>{title}</Text>
            <Text style={styles.actionSubtitle}>{subtitle}</Text>
          </View>
          <View style={styles.actionRight}>
            {badge && (
              <View style={[styles.badge, { backgroundColor: GoGoColors.error }]}>
                <Text style={styles.badgeText}>{badge}</Text>
              </View>
            )}
            <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function AdminDashboard() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { videos } = useAppSelector((state) => state.video);
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [showPlatformSettings, setShowPlatformSettings] = useState(false);
  const [showContentModeration, setShowContentModeration] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showFinancialDashboard, setShowFinancialDashboard] = useState(false);
  const [showSystemHealth, setShowSystemHealth] = useState(false);
  const [showPlatformAnalytics, setShowPlatformAnalytics] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Mock admin stats - in real app, fetch from API
  const adminStats = {
    totalUsers: '12.4K',
    totalVideos: '8.7K',
    totalCreators: '1.2K',
    revenue: '$45.2K',
    activeUsers: '3.8K',
    reports: '23',
    pendingReviews: '15',
    serverLoad: '67%',
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => setRefreshing(false), 2000);
  };

  // Admin Actions
  const handleUserManagement = () => {
    hapticFeedback.light();
    setShowUserManagement(true);
  };

  const handleContentModeration = () => {
    hapticFeedback.light();
    setShowContentModeration(true);
  };

  const handleAnalytics = () => {
    hapticFeedback.light();
    setShowPlatformAnalytics(true);
  };

  const handleReports = () => {
    hapticFeedback.light();
    Alert.alert('Reports & Issues', 'Reports management coming soon!');
  };

  const handleFinancials = () => {
    hapticFeedback.light();
    setShowFinancialDashboard(true);
  };

  const handleSystemHealth = () => {
    hapticFeedback.light();
    setShowSystemHealth(true);
  };

  const handleSettings = () => {
    hapticFeedback.light();
    setShowPlatformSettings(true);
  };

  const handleCreatorProgram = () => {
    hapticFeedback.light();
    Alert.alert('Creator Program', 'Creator program management coming soon!');
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await dispatch(signOut()).unwrap();
      hapticFeedback.success();
      // After logout, AppNavigator will redirect to AuthScreen (login) automatically
      // Optionally, you can show a spinner or splash until redirect
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Logout Failed', typeof error === 'string' ? error : 'An error occurred');
    } finally {
      setIsLoggingOut(false);
    }
  };

  if (!user) {
    // If user is null after logout, show nothing or a loading spinner until AppNavigator redirects
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: GoGoColors.backgroundDark }}>
        <ActivityIndicator size="large" color={GoGoColors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[GoGoColors.primary]}
            tintColor={GoGoColors.primary}
          />
        }
      >
        {/* Header */}
        <Animated.View style={styles.header} entering={FadeIn}>
          <LinearGradient
            colors={["#FFFFFF", "#F7F9FA", "#FFFFFF"]}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <View style={styles.headerTop}>
                <View>
                  <Text style={styles.welcomeText}>Admin Dashboard</Text>
                  <Text style={styles.headerTitle}>Platform Overview 🛠️</Text>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                  <TouchableOpacity style={styles.settingsButton} onPress={handleSettings} disabled={isLoggingOut}>
                    <Ionicons name="settings-outline" size={24} color={GoGoColors.textPrimary} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.logoutButton} onPress={handleLogout} disabled={isLoggingOut}>
                    {isLoggingOut ? (
                      <Animated.View entering={FadeIn}>
                        <ActivityIndicator size="small" color={GoGoColors.error} />
                      </Animated.View>
                    ) : (
                      <Ionicons name="log-out-outline" size={24} color={GoGoColors.error} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Period Selector */}
        <Animated.View style={styles.periodSelector} entering={SlideInDown.delay(200)}>
          <Text style={styles.sectionTitle}>Time Period</Text>
          <View style={styles.periodButtons}>
            {(['today', 'week', 'month'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  selectedPeriod === period && styles.periodButtonActive
                ]}
                onPress={() => {
                  hapticFeedback.light();
                  setSelectedPeriod(period);
                }}
              >
                <Text style={[
                  styles.periodButtonText,
                  selectedPeriod === period && styles.periodButtonTextActive
                ]}>
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Platform Stats */}
        <Animated.View style={styles.statsSection} entering={SlideInDown.delay(400)}>
          <Text style={styles.sectionTitle}>Platform Statistics</Text>
          <View style={styles.statsGrid}>
            <AdminStatsCard
              icon="people"
              value={adminStats.totalUsers}
              label="Total Users"
              color={GoGoColors.primary}
              index={0}
              trend="+5.2%"
              onPress={handleUserManagement}
            />
            <AdminStatsCard
              icon="videocam"
              value={adminStats.totalVideos}
              label="Total Videos"
              color={GoGoColors.highlightGold}
              index={1}
              trend="+12.8%"
              onPress={handleContentModeration}
            />
            <AdminStatsCard
              icon="person-add"
              value={adminStats.totalCreators}
              label="Creators"
              color={GoGoColors.success}
              index={2}
              trend="+8.1%"
              onPress={handleCreatorProgram}
            />
            <AdminStatsCard
              icon="cash"
              value={adminStats.revenue}
              label="Revenue"
              color={GoGoColors.error}
              index={3}
              trend="+15.7%"
              onPress={handleFinancials}
            />
          </View>
        </Animated.View>

        {/* Admin Actions */}
        <Animated.View style={styles.actionsSection} entering={SlideInDown.delay(600)}>
          <Text style={styles.sectionTitle}>Admin Actions</Text>
          <View style={styles.actionsGrid}>
            <AdminActionCard
              icon="shield-checkmark"
              title="Content Moderation"
              subtitle="Review flagged content"
              color={GoGoColors.primary}
              onPress={handleContentModeration}
              index={0}
              badge={adminStats.pendingReviews}
            />
            <AdminActionCard
              icon="warning"
              title="Reports & Issues"
              subtitle="Handle user reports"
              color={GoGoColors.error}
              onPress={handleReports}
              index={1}
              badge={adminStats.reports}
            />
            <AdminActionCard
              icon="bar-chart"
              title="Platform Analytics"
              subtitle="View detailed insights"
              color={GoGoColors.highlightGold}
              onPress={handleAnalytics}
              index={2}
            />
            <AdminActionCard
              icon="server"
              title="System Health"
              subtitle="Monitor server status"
              color={GoGoColors.success}
              onPress={handleSystemHealth}
              index={3}
            />
            <AdminActionCard
              icon="people-circle"
              title="User Management"
              subtitle="Manage user accounts"
              color={GoGoColors.primary}
              onPress={handleUserManagement}
              index={4}
            />
            <AdminActionCard
              icon="wallet"
              title="Financial Dashboard"
              subtitle="Revenue and payments"
              color={GoGoColors.error}
              onPress={handleFinancials}
              index={5}
            />
          </View>
        </Animated.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
      {/* Platform Settings Modal */}
      <Modal
        visible={showPlatformSettings}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowPlatformSettings(false)}
      >
        <PlatformSettingsScreen onClose={() => setShowPlatformSettings(false)} />
      </Modal>
      {/* Content Moderation Modal */}
      <Modal
        visible={showContentModeration}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowContentModeration(false)}
      >
        <ContentModerationScreen onClose={() => setShowContentModeration(false)} />
      </Modal>
      {/* User Management Modal */}
      <Modal
        visible={showUserManagement}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowUserManagement(false)}
      >
        <UserManagementScreen onClose={() => setShowUserManagement(false)} />
      </Modal>
      {/* Financial Dashboard Modal */}
      <Modal
        visible={showFinancialDashboard}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowFinancialDashboard(false)}
      >
        <FinancialDashboardScreen onClose={() => setShowFinancialDashboard(false)} />
      </Modal>
      {/* System Health Modal */}
      <Modal
        visible={showSystemHealth}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowSystemHealth(false)}
      >
        <SystemHealthScreen onClose={() => setShowSystemHealth(false)} />
      </Modal>
      {/* Platform Analytics Modal */}
      <Modal
        visible={showPlatformAnalytics}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowPlatformAnalytics(false)}
      >
        <PlatformAnalyticsScreen onClose={() => setShowPlatformAnalytics(false)} />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  scrollContainer: {
    flex: 1,
  },
  // Header Styles
  header: {
    marginBottom: 24,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 32,
    paddingHorizontal: 20,
  },
  headerContent: {
    flex: 1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  logoutButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 0, 0, 0.08)',
    marginLeft: 4,
  },
  // Section Styles
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  // Period Selector
  periodSelector: {
    marginBottom: 24,
  },
  periodButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  // Stats Styles
  statsSection: {
    marginBottom: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 16,
  },
  statsCard: {
    width: (width - 56) / 2,
    borderRadius: 16,
    overflow: 'hidden',
  },
  statsCardTouchable: {
    flex: 1,
  },
  statsGradient: {
    padding: 20,
    alignItems: 'center',
  },
  statsIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Actions Styles
  actionsSection: {
    marginBottom: 32,
  },
  actionsGrid: {
    paddingHorizontal: 20,
    gap: 12,
  },
  actionCard: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  actionTouchable: {
    flex: 1,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  actionRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  bottomSpacing: {
    height: 32,
  },
});
