import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  StatusBar,
  FlatList,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown, SlideInRight } from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

// Mock data for flagged content
const mockReports = [
  {
    id: '1',
    type: 'Video',
    contentTitle: 'Funny Cat Compilation',
    contentThumbnail: 'https://via.placeholder.com/120x80',
    reportedBy: 'user123',
    reason: 'Inappropriate content',
    date: '2024-06-01',
    status: 'Pending',
    flaggedUser: 'creatorA',
  },
  {
    id: '2',
    type: 'Comment',
    contentTitle: '"This is spam!"',
    contentThumbnail: '',
    reportedBy: 'user456',
    reason: 'Spam',
    date: '2024-06-02',
    status: 'Reviewed',
    flaggedUser: 'user789',
  },
  {
    id: '3',
    type: 'Video',
    contentTitle: 'Prank Gone Wrong',
    contentThumbnail: 'https://via.placeholder.com/120x80',
    reportedBy: 'user999',
    reason: 'Violence',
    date: '2024-06-03',
    status: 'Pending',
    flaggedUser: 'creatorB',
  },
];

const statusColors = {
  Pending: GoGoColors.warning,
  Reviewed: GoGoColors.success,
  Removed: GoGoColors.error,
};

interface Props {
  onClose: () => void;
}

export default function ContentModerationScreen({ onClose }: Props) {
  const [search, setSearch] = useState('');
  const [filterStatus, setFilterStatus] = useState<'All' | 'Pending' | 'Reviewed' | 'Removed'>('All');
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);

  const filteredReports = mockReports.filter((report) => {
    const matchesStatus = filterStatus === 'All' || report.status === filterStatus;
    const matchesSearch =
      report.contentTitle.toLowerCase().includes(search.toLowerCase()) ||
      report.reason.toLowerCase().includes(search.toLowerCase()) ||
      report.reportedBy.toLowerCase().includes(search.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const handleAction = async (action: string) => {
    setIsActionLoading(true);
    hapticFeedback.light();
    setTimeout(() => {
      setIsActionLoading(false);
      setSelectedReport(null);
      hapticFeedback.success();
    }, 1000);
  };

  // Height of the header (nav) including padding. Adjust if you change header size.
  const HEADER_HEIGHT = 100;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Content Moderation</Text>
              <Text style={styles.headerSubtitle}>Review flagged content and take action</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      {/* Filters are now in ListHeaderComponent */}
      {/* Reports List */}
      <FlatList
        data={filteredReports}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[styles.listContent, { paddingTop: HEADER_HEIGHT }]}
        ListHeaderComponent={
          <View style={styles.filters}>
            <View style={styles.filterRow}>
              <View style={styles.statusFilterGroup}>
                {(['All', 'Pending', 'Reviewed', 'Removed'] as const).map((status) => (
                  <TouchableOpacity
                    key={status}
                    style={[styles.statusFilter, filterStatus === status && styles.statusFilterActive]}
                    onPress={() => setFilterStatus(status)}
                  >
                    <Text style={[styles.statusFilterText, filterStatus === status && styles.statusFilterTextActive]}>
                      {status}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <View style={styles.searchBox}>
                <Ionicons name="search" size={18} color={GoGoColors.textMuted} />
                <TextInput
                  style={styles.searchInput}
                  value={search}
                  onChangeText={setSearch}
                  placeholder="Search reports..."
                  placeholderTextColor={GoGoColors.textMuted}
                />
              </View>
            </View>
          </View>
        }
        renderItem={({ item, index }) => (
          <Animated.View entering={SlideInDown.delay(index * 80)}>
            <TouchableOpacity
              style={styles.reportCard}
              onPress={() => setSelectedReport(item)}
              activeOpacity={0.85}
            >
              <View style={styles.reportLeft}>
                {item.contentThumbnail ? (
                  <Image source={{ uri: item.contentThumbnail }} style={styles.reportThumbnail} />
                ) : (
                  <View style={styles.reportIconPlaceholder}>
                    <Ionicons name="chatbubble-ellipses-outline" size={32} color={GoGoColors.textMuted} />
                  </View>
                )}
              </View>
              <View style={styles.reportCenter}>
                <Text style={styles.reportTitle} numberOfLines={1}>{item.contentTitle}</Text>
                <Text style={styles.reportReason} numberOfLines={1}>{item.reason}</Text>
                <Text style={styles.reportMeta}>
                  By <Text style={styles.reportUser}>{item.reportedBy}</Text> • {item.date}
                </Text>
              </View>
              <View style={styles.reportRight}>
                <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                  <Text style={styles.statusBadgeText}>{item.status}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
              </View>
            </TouchableOpacity>
          </Animated.View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="checkmark-done-circle-outline" size={48} color={GoGoColors.success} />
            <Text style={styles.emptyText}>No reports found.</Text>
          </View>
        }
      />
      {/* Report Details Modal */}
      <Modal
        visible={!!selectedReport}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSelectedReport(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedReport && (
              <>
                <Text style={styles.modalTitle}>Report Details</Text>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Content</Text>
                  <Text style={styles.modalValue}>{selectedReport.contentTitle}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Type</Text>
                  <Text style={styles.modalValue}>{selectedReport.type}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Reason</Text>
                  <Text style={styles.modalValue}>{selectedReport.reason}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Reported By</Text>
                  <Text style={styles.modalValue}>{selectedReport.reportedBy}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Flagged User</Text>
                  <Text style={styles.modalValue}>{selectedReport.flaggedUser}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Date</Text>
                  <Text style={styles.modalValue}>{selectedReport.date}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Status</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[selectedReport.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{selectedReport.status}</Text>
                  </View>
                </View>
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.success }]}
                    onPress={() => handleAction('approve')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="checkmark" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Approve</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.error }]}
                    onPress={() => handleAction('remove')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="trash" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Remove</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.primary }]}
                    onPress={() => handleAction('ban')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="ban" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Ban User</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.info }]}
                    onPress={() => handleAction('reviewed')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="eye" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Mark Reviewed</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
                <TouchableOpacity style={styles.modalCloseButton} onPress={() => setSelectedReport(null)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  filters: {
    // marginTop removed; handled by FlatList paddingTop
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
  },
  statusFilterGroup: {
    flexDirection: 'row',
    gap: 6,
  },
  statusFilter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: GoGoColors.backgroundLight,
  },
  statusFilterActive: {
    backgroundColor: GoGoColors.primary,
  },
  statusFilterText: {
    fontSize: 13,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  statusFilterTextActive: {
    color: '#fff',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    flex: 1,
    marginLeft: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    color: GoGoColors.textPrimary,
    marginLeft: 8,
    paddingVertical: 8,
  },
  listContent: {
    paddingBottom: 40,
    paddingHorizontal: 10,
  },
  reportCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  reportLeft: {
    marginRight: 14,
  },
  reportThumbnail: {
    width: 60,
    height: 40,
    borderRadius: 8,
    backgroundColor: GoGoColors.backgroundLight,
  },
  reportIconPlaceholder: {
    width: 60,
    height: 40,
    borderRadius: 8,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportCenter: {
    flex: 1,
  },
  reportTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  reportReason: {
    fontSize: 13,
    color: GoGoColors.error,
    marginBottom: 2,
  },
  reportMeta: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  reportUser: {
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  reportRight: {
    alignItems: 'center',
    marginLeft: 10,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 12,
  },
  modalLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  modalValue: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 18,
    marginBottom: 8,
  },
  modalActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 16,
    marginBottom: 6,
  },
  modalActionText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  modalCloseButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 15,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
}); 