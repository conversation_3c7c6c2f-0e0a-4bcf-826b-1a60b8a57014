import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

// Helper for MWK currency formatting
const formatMWK = (amount: number) =>
  `MWK ${amount.toLocaleString('en-MW', { minimumFractionDigits: 0 })}`;

// Helper for date formatting (Malawi: dd/MM/yyyy)
const formatDate = (date: string) => {
  const d = new Date(date);
  return d.toLocaleDateString('en-GB');
};

// Mock financial data
const financialStats = {
  totalRevenue: 12500000,
  totalPayouts: 8200000,
  totalCommissions: 1800000,
  outstanding: 2500000,
};

const recentTransactions = [
  {
    id: '1',
    type: 'Payout',
    user: 'creatorA',
    amount: 500000,
    date: '2024-06-01',
    status: 'Completed',
  },
  {
    id: '2',
    type: 'Commission',
    user: 'GoGo',
    amount: 120000,
    date: '2024-06-02',
    status: 'Completed',
  },
  {
    id: '3',
    type: 'Payout',
    user: 'creatorB',
    amount: 300000,
    date: '2024-06-03',
    status: 'Pending',
  },
  {
    id: '4',
    type: 'Revenue',
    user: 'user123',
    amount: 2000,
    date: '2024-06-04',
    status: 'Completed',
  },
];

const payoutRequests = [
  {
    id: '1',
    creator: 'creatorA',
    amount: 150000,
    requested: '2024-06-05',
    status: 'Pending',
  },
  {
    id: '2',
    creator: 'creatorC',
    amount: 90000,
    requested: '2024-06-06',
    status: 'Pending',
  },
];

const statusColors = {
  Completed: GoGoColors.success,
  Pending: GoGoColors.warning,
  Failed: GoGoColors.error,
};

interface Props {
  onClose: () => void;
}

export default function FinancialDashboardScreen({ onClose }: Props) {
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<any>(null);

  // Height of the header (nav) including padding. Adjust if you change header size.
  const HEADER_HEIGHT = 100;

  const handlePayoutAction = async (action: string) => {
    setIsActionLoading(true);
    hapticFeedback.light();
    setTimeout(() => {
      setIsActionLoading(false);
      setSelectedRequest(null);
      hapticFeedback.success();
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Financial Dashboard</Text>
              <Text style={styles.headerSubtitle}>Overview of platform finances (MWK)</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      {/* Financial Stats */}
      <View style={[styles.statsSection, { paddingTop: HEADER_HEIGHT }]}> 
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.primary + '10' }]}> 
            <Ionicons name="cash" size={28} color={GoGoColors.primary} />
            <Text style={styles.statsLabel}>Total Revenue</Text>
            <Text style={styles.statsValue}>{formatMWK(financialStats.totalRevenue)}</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.success + '10' }]}> 
            <Ionicons name="wallet" size={28} color={GoGoColors.success} />
            <Text style={styles.statsLabel}>Total Payouts</Text>
            <Text style={styles.statsValue}>{formatMWK(financialStats.totalPayouts)}</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.warning + '10' }]}> 
            <Ionicons name="trending-up" size={28} color={GoGoColors.warning} />
            <Text style={styles.statsLabel}>Commissions</Text>
            <Text style={styles.statsValue}>{formatMWK(financialStats.totalCommissions)}</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.error + '10' }]}> 
            <Ionicons name="alert-circle" size={28} color={GoGoColors.error} />
            <Text style={styles.statsLabel}>Outstanding</Text>
            <Text style={styles.statsValue}>{formatMWK(financialStats.outstanding)}</Text>
          </View>
        </View>
      </View>
      {/* Recent Transactions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Transactions</Text>
        <FlatList
          data={recentTransactions}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item, index }) => (
            <Animated.View entering={SlideInDown.delay(index * 60)}>
              <View style={styles.transactionCard}>
                <View style={styles.transactionLeft}>
                  <Ionicons
                    name={item.type === 'Payout' ? 'wallet' : item.type === 'Commission' ? 'trending-up' : 'cash'}
                    size={24}
                    color={item.type === 'Payout' ? GoGoColors.success : item.type === 'Commission' ? GoGoColors.warning : GoGoColors.primary}
                  />
                </View>
                <View style={styles.transactionCenter}>
                  <Text style={styles.transactionType}>{item.type}</Text>
                  <Text style={styles.transactionUser}>{item.user}</Text>
                  <Text style={styles.transactionDate}>{formatDate(item.date)}</Text>
                </View>
                <View style={styles.transactionRight}>
                  <Text style={styles.transactionAmount}>{formatMWK(item.amount)}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{item.status}</Text>
                  </View>
                </View>
              </View>
            </Animated.View>
          )}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons name="receipt-outline" size={48} color={GoGoColors.textMuted} />
              <Text style={styles.emptyText}>No transactions found.</Text>
            </View>
          }
        />
      </View>
      {/* Payout Requests */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Payout Requests</Text>
        <FlatList
          data={payoutRequests}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item, index }) => (
            <Animated.View entering={SlideInDown.delay(index * 60)}>
              <TouchableOpacity
                style={styles.payoutCard}
                onPress={() => setSelectedRequest(item)}
                activeOpacity={0.85}
              >
                <View style={styles.payoutLeft}>
                  <Ionicons name="person" size={24} color={GoGoColors.primary} />
                </View>
                <View style={styles.payoutCenter}>
                  <Text style={styles.payoutCreator}>{item.creator}</Text>
                  <Text style={styles.payoutDate}>Requested {formatDate(item.requested)}</Text>
                </View>
                <View style={styles.payoutRight}>
                  <Text style={styles.payoutAmount}>{formatMWK(item.amount)}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{item.status}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            </Animated.View>
          )}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons name="wallet-outline" size={48} color={GoGoColors.textMuted} />
              <Text style={styles.emptyText}>No payout requests.</Text>
            </View>
          }
        />
      </View>
      {/* Payout Request Modal */}
      <Modal
        visible={!!selectedRequest}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSelectedRequest(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedRequest && (
              <>
                <Text style={styles.modalTitle}>Payout Request</Text>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Creator</Text>
                  <Text style={styles.modalValue}>{selectedRequest.creator}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Amount</Text>
                  <Text style={styles.modalValue}>{formatMWK(selectedRequest.amount)}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Requested</Text>
                  <Text style={styles.modalValue}>{formatDate(selectedRequest.requested)}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Status</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[selectedRequest.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{selectedRequest.status}</Text>
                  </View>
                </View>
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.success }]}
                    onPress={() => handlePayoutAction('approve')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="checkmark" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Approve</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.error }]}
                    onPress={() => handlePayoutAction('reject')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="close" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Reject</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
                <TouchableOpacity style={styles.modalCloseButton} onPress={() => setSelectedRequest(null)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statsSection: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  statsCard: {
    flex: 1,
    borderRadius: 16,
    padding: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  statsLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginTop: 8,
    marginBottom: 2,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  listContent: {
    paddingBottom: 24,
  },
  transactionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionLeft: {
    marginRight: 14,
  },
  transactionCenter: {
    flex: 1,
  },
  transactionType: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  transactionUser: {
    fontSize: 13,
    color: GoGoColors.primary,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  transactionRight: {
    alignItems: 'center',
    marginLeft: 10,
  },
  transactionAmount: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  payoutCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  payoutLeft: {
    marginRight: 14,
  },
  payoutCenter: {
    flex: 1,
  },
  payoutCreator: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  payoutDate: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  payoutRight: {
    alignItems: 'center',
    marginLeft: 10,
  },
  payoutAmount: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 12,
  },
  modalLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  modalValue: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 18,
    marginBottom: 8,
  },
  modalActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 16,
    marginBottom: 6,
  },
  modalActionText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  modalCloseButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 15,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
}); 