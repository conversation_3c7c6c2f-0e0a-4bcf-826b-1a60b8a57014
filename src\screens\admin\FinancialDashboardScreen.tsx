import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeIn,
  SlideInDown,
  SlideInRight,
  useAnimatedStyle,
  withSpring,
  useSharedValue,
} from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

const { width } = Dimensions.get('window');

// Helper for MWK currency formatting
const formatMWK = (amount: number) =>
  `MWK ${amount.toLocaleString('en-MW', { minimumFractionDigits: 0 })}`;

// Helper for date formatting (Malawi: dd/MM/yyyy)
const formatDate = (date: string) => {
  const d = new Date(date);
  return d.toLocaleDateString('en-GB');
};

// Financial data will be loaded from Supabase database
// TODO: Implement real financial analytics with databaseService
const financialStats = {
  totalRevenue: 2450000, // MWK 2.45M
  totalPayouts: 1715000, // MWK 1.715M
  totalCommissions: 367500, // MWK 367.5K (15% of revenue)
  outstanding: 367500, // MWK 367.5K
  monthlyGrowth: 12.5, // 12.5% growth
  activeCreators: 156,
  totalTransactions: 1247,
  averageTransaction: 1965, // MWK 1,965
};

const recentTransactions = [
  {
    id: '1',
    type: 'Subscription',
    user: 'Alice Banda',
    creator: 'John Phiri',
    amount: 1500,
    date: '2024-01-14',
    status: 'Completed',
    method: 'Airtel Money',
  },
  {
    id: '2',
    type: 'Pay-per-view',
    user: 'Moses Tembo',
    creator: 'Grace Mwale',
    amount: 300,
    date: '2024-01-14',
    status: 'Completed',
    method: 'TNM Mpamba',
  },
  {
    id: '3',
    type: 'Subscription',
    user: 'Faith Chirwa',
    creator: 'David Kachali',
    amount: 1500,
    date: '2024-01-13',
    status: 'Pending',
    method: 'Airtel Money',
  },
  {
    id: '4',
    type: 'Pay-per-view',
    user: 'Peter Nyirenda',
    creator: 'Sarah Mvula',
    amount: 300,
    date: '2024-01-13',
    status: 'Completed',
    method: 'TNM Mpamba',
  },
];

const payoutRequests = [
  {
    id: '1',
    creator: 'John Phiri',
    amount: 127500,
    requested: '2024-01-12',
    status: 'Pending',
    method: 'Airtel Money',
    phone: '+265 991 234 567',
  },
  {
    id: '2',
    creator: 'Grace Mwale',
    amount: 85200,
    requested: '2024-01-11',
    status: 'Processing',
    method: 'TNM Mpamba',
    phone: '+265 995 876 543',
  },
  {
    id: '3',
    creator: 'David Kachali',
    amount: 156750,
    requested: '2024-01-10',
    status: 'Completed',
    method: 'Airtel Money',
    phone: '+265 992 345 678',
  },
];

const statusColors = {
  Completed: '#10B981',
  Pending: '#F59E0B',
  Processing: '#3B82F6',
  Failed: '#EF4444',
};

const transactionTypeColors = {
  Subscription: '#8B5CF6',
  'Pay-per-view': '#06B6D4',
  Payout: '#10B981',
  Commission: '#F59E0B',
};

interface Props {
  onClose: () => void;
}

// Modern Financial Card Component
const FinancialCard = ({
  title,
  value,
  change,
  icon,
  color,
  index
}: {
  title: string;
  value: string;
  change?: string;
  icon: string;
  color: string;
  index: number;
}) => {
  const scale = useSharedValue(0.95);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withSpring(scale.value) }],
  }));

  return (
    <Animated.View
      style={[styles.modernCard, animatedStyle]}
      entering={SlideInDown.delay(index * 100)}
    >
      <LinearGradient
        colors={[color + '15', color + '05']}
        style={styles.cardGradient}
      >
        <View style={styles.cardHeader}>
          <View style={[styles.cardIcon, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon as any} size={24} color={color} />
          </View>
          {change && (
            <View style={[styles.changeIndicator, { backgroundColor: change.startsWith('+') ? '#10B981' : '#EF4444' }]}>
              <Text style={styles.changeText}>{change}</Text>
            </View>
          )}
        </View>
        <Text style={styles.cardTitle}>{title}</Text>
        <Text style={styles.cardValue}>{value}</Text>
      </LinearGradient>
    </Animated.View>
  );
};

// Transaction Item Component
const TransactionItem = ({ transaction, index }: { transaction: any; index: number }) => (
  <Animated.View
    style={styles.transactionItem}
    entering={SlideInRight.delay(index * 50)}
  >
    <View style={styles.transactionLeft}>
      <View style={[styles.transactionIcon, { backgroundColor: transactionTypeColors[transaction.type] + '20' }]}>
        <Ionicons
          name={transaction.type === 'Subscription' ? 'person-add' : 'play-circle'}
          size={20}
          color={transactionTypeColors[transaction.type]}
        />
      </View>
      <View style={styles.transactionDetails}>
        <Text style={styles.transactionUser}>{transaction.user}</Text>
        <Text style={styles.transactionCreator}>to {transaction.creator}</Text>
        <Text style={styles.transactionMethod}>{transaction.method}</Text>
      </View>
    </View>
    <View style={styles.transactionRight}>
      <Text style={styles.transactionAmount}>{formatMWK(transaction.amount)}</Text>
      <View style={[styles.statusBadge, { backgroundColor: statusColors[transaction.status] + '20' }]}>
        <Text style={[styles.statusText, { color: statusColors[transaction.status] }]}>
          {transaction.status}
        </Text>
      </View>
    </View>
  </Animated.View>
);

// Payout Request Component
const PayoutRequestItem = ({ request, index, onApprove, onReject }: {
  request: any;
  index: number;
  onApprove: () => void;
  onReject: () => void;
}) => (
  <Animated.View
    style={styles.payoutItem}
    entering={SlideInRight.delay(index * 50)}
  >
    <View style={styles.payoutHeader}>
      <View style={styles.payoutCreator}>
        <Text style={styles.payoutCreatorName}>{request.creator}</Text>
        <Text style={styles.payoutAmount}>{formatMWK(request.amount)}</Text>
      </View>
      <View style={[styles.statusBadge, { backgroundColor: statusColors[request.status] + '20' }]}>
        <Text style={[styles.statusText, { color: statusColors[request.status] }]}>
          {request.status}
        </Text>
      </View>
    </View>
    <View style={styles.payoutDetails}>
      <Text style={styles.payoutMethod}>{request.method}: {request.phone}</Text>
      <Text style={styles.payoutDate}>Requested: {formatDate(request.requested)}</Text>
    </View>
    {request.status === 'Pending' && (
      <View style={styles.payoutActions}>
        <TouchableOpacity style={styles.approveButton} onPress={onApprove}>
          <Text style={styles.approveButtonText}>Approve</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.rejectButton} onPress={onReject}>
          <Text style={styles.rejectButtonText}>Reject</Text>
        </TouchableOpacity>
      </View>
    )}
  </Animated.View>
);

export default function FinancialDashboardScreen({ onClose }: Props) {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'transactions' | 'payouts'>('overview');
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('month');

  const onRefresh = async () => {
    setRefreshing(true);
    hapticFeedback.light();
    setTimeout(() => setRefreshing(false), 2000);
  };

  const handleApproveRequest = (requestId: string) => {
    hapticFeedback.success();
  };

  const handleRejectRequest = (requestId: string) => {
    hapticFeedback.error();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Modern Header */}
      <Animated.View style={styles.modernHeader} entering={FadeIn}>
        <LinearGradient
          colors={['#667EEA', '#764BA2']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.modernHeaderTitle}>Financial Dashboard</Text>
              <Text style={styles.modernHeaderSubtitle}>Platform Revenue Analytics</Text>
            </View>
            <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
              <Ionicons name="refresh" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Period Selector */}
        <Animated.View style={styles.periodSelector} entering={SlideInDown.delay(100)}>
          {(['today', 'week', 'month', 'year'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive
              ]}
              onPress={() => {
                setSelectedPeriod(period);
                hapticFeedback.light();
              }}
            >
              <Text style={[
                styles.periodButtonText,
                selectedPeriod === period && styles.periodButtonTextActive
              ]}>
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Financial Cards Grid */}
        <View style={styles.cardsGrid}>
          <FinancialCard
            title="Total Revenue"
            value={formatMWK(financialStats.totalRevenue)}
            change="+12.5%"
            icon="trending-up"
            color="#667EEA"
            index={0}
          />
          <FinancialCard
            title="Total Payouts"
            value={formatMWK(financialStats.totalPayouts)}
            change="+8.3%"
            icon="wallet"
            color="#10B981"
            index={1}
          />
          <FinancialCard
            title="Platform Commission"
            value={formatMWK(financialStats.totalCommissions)}
            change="+15.2%"
            icon="cash"
            color="#F59E0B"
            index={2}
          />
          <FinancialCard
            title="Outstanding"
            value={formatMWK(financialStats.outstanding)}
            change="-5.1%"
            icon="alert-circle"
            color="#EF4444"
            index={3}
          />
        </View>

        {/* Quick Stats Row */}
        <Animated.View style={styles.quickStats} entering={SlideInDown.delay(400)}>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{financialStats.activeCreators}</Text>
            <Text style={styles.quickStatLabel}>Active Creators</Text>
          </View>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{financialStats.totalTransactions}</Text>
            <Text style={styles.quickStatLabel}>Transactions</Text>
          </View>
          <View style={styles.quickStatItem}>
            <Text style={styles.quickStatValue}>{formatMWK(financialStats.averageTransaction)}</Text>
            <Text style={styles.quickStatLabel}>Avg. Transaction</Text>
          </View>
        </Animated.View>
        {/* Tab Navigation */}
        <Animated.View style={styles.tabNavigation} entering={SlideInDown.delay(500)}>
          {(['transactions', 'payouts'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[
                styles.tabButton,
                selectedTab === tab && styles.tabButtonActive
              ]}
              onPress={() => {
                setSelectedTab(tab);
                hapticFeedback.light();
              }}
            >
              <Ionicons
                name={tab === 'transactions' ? 'receipt' : 'wallet'}
                size={20}
                color={selectedTab === tab ? '#667EEA' : '#6B7280'}
              />
              <Text style={[
                styles.tabButtonText,
                selectedTab === tab && styles.tabButtonTextActive
              ]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Tab Content */}
        {selectedTab === 'transactions' && (
          <Animated.View style={styles.tabContent} entering={FadeIn}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Transactions</Text>
              <TouchableOpacity style={styles.viewAllButton}>
                <Text style={styles.viewAllText}>View All</Text>
                <Ionicons name="chevron-forward" size={16} color="#667EEA" />
              </TouchableOpacity>
            </View>
            {recentTransactions.map((transaction, index) => (
              <TransactionItem
                key={transaction.id}
                transaction={transaction}
                index={index}
              />
            ))}
          </Animated.View>
        )}

        {selectedTab === 'payouts' && (
          <Animated.View style={styles.tabContent} entering={FadeIn}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Payout Requests</Text>
              <View style={styles.pendingBadge}>
                <Text style={styles.pendingBadgeText}>
                  {payoutRequests.filter(r => r.status === 'Pending').length} Pending
                </Text>
              </View>
            </View>
            {payoutRequests.map((request, index) => (
              <PayoutRequestItem
                key={request.id}
                request={request}
                index={index}
                onApprove={() => handleApproveRequest(request.id)}
                onReject={() => handleRejectRequest(request.id)}
              />
            ))}
          </Animated.View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  modernHeader: {
    paddingTop: 44,
    paddingBottom: 0,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  modernHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  modernHeaderSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  // Period Selector
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 24,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#667EEA',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  // Cards Grid
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 16,
  },
  modernCard: {
    width: (width - 56) / 2,
    marginBottom: 16,
  },
  cardGradient: {
    borderRadius: 16,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  changeIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  changeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  cardTitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  // Quick Stats
  quickStats: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 20,
    marginBottom: 24,
    paddingVertical: 20,
  },
  quickStatItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  quickStatLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  // Tab Navigation
  tabNavigation: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  tabButtonActive: {
    backgroundColor: '#667EEA',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  tabButtonTextActive: {
    color: '#FFFFFF',
  },
  // Tab Content
  tabContent: {
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#667EEA',
  },
  pendingBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  pendingBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#D97706',
  },
  // Transaction Item Styles
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionUser: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  transactionCreator: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  transactionMethod: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Payout Item Styles
  payoutItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  payoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  payoutCreator: {
    flex: 1,
  },
  payoutCreatorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  payoutAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#10B981',
  },
  payoutDetails: {
    marginBottom: 16,
  },
  payoutMethod: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  payoutDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  payoutActions: {
    flexDirection: 'row',
    gap: 12,
  },
  approveButton: {
    flex: 1,
    backgroundColor: '#10B981',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  approveButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#EF4444',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  rejectButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 100,
  },


}); 