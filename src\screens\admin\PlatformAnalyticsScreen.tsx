import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

const mockAnalytics = {
  userGrowth: [1200, 1500, 1800, 2200, 2600, 3200, 4000], // per month
  engagement: [300, 450, 600, 800, 1200, 1600, 2100], // active users
  topVideos: [
    { id: '1', title: 'Funny Cat Compilation', views: 12000 },
    { id: '2', title: 'Epic Prank', views: 9500 },
    { id: '3', title: 'Music Video', views: 8700 },
  ],
  trafficSources: [
    { source: 'Direct', percent: 40 },
    { source: 'Social', percent: 35 },
    { source: 'Search', percent: 15 },
    { source: 'Referral', percent: 10 },
  ],
  revenueTrend: [200000, 350000, 500000, 700000, 900000, 1200000, 1500000], // MWK per month
  months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
};

interface Props {
  onClose: () => void;
}

export default function PlatformAnalyticsScreen({ onClose }: Props) {
  const HEADER_HEIGHT = 100;
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Platform Analytics</Text>
              <Text style={styles.headerSubtitle}>User growth, engagement, and trends</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      <FlatList
        data={mockAnalytics.topVideos}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingTop: HEADER_HEIGHT, paddingBottom: 32, ...styles.listContent }}
        ListHeaderComponent={
          <>
            {/* User Growth */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>User Growth</Text>
              <View style={styles.analyticsRow}>
                {mockAnalytics.userGrowth.map((val, idx) => (
                  <View key={idx} style={styles.analyticsBarContainer}>
                    <View style={[styles.analyticsBar, { height: val / 50, backgroundColor: GoGoColors.primary }]} />
                    <Text style={styles.analyticsBarLabel}>{mockAnalytics.months[idx]}</Text>
                  </View>
                ))}
              </View>
            </View>
            {/* Engagement */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Active Users</Text>
              <View style={styles.analyticsRow}>
                {mockAnalytics.engagement.map((val, idx) => (
                  <View key={idx} style={styles.analyticsBarContainer}>
                    <View style={[styles.analyticsBar, { height: val / 20, backgroundColor: GoGoColors.success }]} />
                    <Text style={styles.analyticsBarLabel}>{mockAnalytics.months[idx]}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        }
        renderItem={({ item }) => (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Videos</Text>
            <View style={styles.videoCard}>
              <Ionicons name="videocam" size={24} color={GoGoColors.primary} style={{ marginRight: 12 }} />
              <View style={{ flex: 1 }}>
                <Text style={styles.videoTitle}>{item.title}</Text>
                <Text style={styles.videoViews}>{item.views.toLocaleString()} views</Text>
              </View>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="videocam-outline" size={48} color={GoGoColors.textMuted} />
            <Text style={styles.emptyText}>No videos found.</Text>
          </View>
        }
        ListFooterComponent={
          <>
            {/* Traffic Sources */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Traffic Sources</Text>
              <View style={styles.trafficRow}>
                {mockAnalytics.trafficSources.map((src, idx) => (
                  <View key={idx} style={styles.trafficSource}>
                    <Ionicons name="globe" size={20} color={GoGoColors.info} style={{ marginRight: 6 }} />
                    <Text style={styles.trafficLabel}>{src.source}</Text>
                    <Text style={styles.trafficPercent}>{src.percent}%</Text>
                  </View>
                ))}
              </View>
            </View>
            {/* Revenue Trend */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Revenue Trend (MWK)</Text>
              <View style={styles.analyticsRow}>
                {mockAnalytics.revenueTrend.map((val, idx) => (
                  <View key={idx} style={styles.analyticsBarContainer}>
                    <View style={[styles.analyticsBar, { height: val / 20000, backgroundColor: GoGoColors.warning }]} />
                    <Text style={styles.analyticsBarLabel}>{mockAnalytics.months[idx]}</Text>
                  </View>
                ))}
              </View>
            </View>
          </>
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  analyticsRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
    minHeight: 80,
  },
  analyticsBarContainer: {
    flex: 1,
    alignItems: 'center',
  },
  analyticsBar: {
    width: 18,
    borderRadius: 8,
    marginBottom: 6,
  },
  analyticsBarLabel: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  videoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  videoTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  videoViews: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
  },
  listContent: {
    paddingBottom: 24,
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  trafficRow: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  trafficSource: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  trafficLabel: {
    fontSize: 13,
    color: GoGoColors.textPrimary,
    marginRight: 4,
  },
  trafficPercent: {
    fontSize: 13,
    color: GoGoColors.info,
    fontWeight: 'bold',
  },
}); 