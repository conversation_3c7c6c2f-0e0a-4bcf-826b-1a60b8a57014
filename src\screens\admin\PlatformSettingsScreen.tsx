import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
  Modal,
  StatusBar,
  Image,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

interface Props {
  onClose: () => void;
}

export default function PlatformSettingsScreen({ onClose }: Props) {
  // Branding
  const [platformName, setPlatformName] = useState('GoGo');
  const [themeColor, setThemeColor] = useState(GoGoColors.primary);
  const [logo, setLogo] = useState<string | null>(null);

  // Monetization
  const [commissionRate, setCommissionRate] = useState('20');
  const [payoutThreshold, setPayoutThreshold] = useState('10000');

  // Content Moderation
  const [autoModeration, setAutoModeration] = useState(true);
  const [flagThreshold, setFlagThreshold] = useState('3');
  const [defaultPolicy, setDefaultPolicy] = useState('No hate speech, nudity, or illegal content.');

  // User Management
  const [allowRegistration, setAllowRegistration] = useState(true);
  const [allowCreators, setAllowCreators] = useState(true);
  const [allowAdmins, setAllowAdmins] = useState(false);
  const [bannedUsers, setBannedUsers] = useState(0);

  // Notifications
  const [emailNotif, setEmailNotif] = useState(true);
  const [pushNotif, setPushNotif] = useState(true);
  const [systemNotif, setSystemNotif] = useState(true);

  // Security
  const [require2FA, setRequire2FA] = useState(false);
  const [passwordPolicy, setPasswordPolicy] = useState('Minimum 8 characters, 1 number, 1 symbol');

  // UI State
  const [isSaving, setIsSaving] = useState(false);
  const [showLogoModal, setShowLogoModal] = useState(false);
  const [tempLogo, setTempLogo] = useState<string | null>(null);

  const handleLogoSelect = async () => {
    hapticFeedback.light();
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      if (!result.canceled && result.assets[0]) {
        setTempLogo(result.assets[0].uri);
        setShowLogoModal(true);
      }
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to select logo.');
    }
  };

  const handleSaveLogo = () => {
    setLogo(tempLogo);
    setShowLogoModal(false);
    hapticFeedback.success();
  };

  const handleSave = async () => {
    setIsSaving(true);
    hapticFeedback.light();
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1200));
      hapticFeedback.success();
      Alert.alert('Settings Saved', 'Platform settings updated successfully!', [
        { text: 'OK', onPress: onClose }
      ]);
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to save settings.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}>
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Platform Settings</Text>
              <Text style={styles.headerSubtitle}>Manage global platform configuration</Text>
            </View>
            <TouchableOpacity
              style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={isSaving}
              activeOpacity={0.8}
            >
              <View style={[styles.saveButtonGradient, { backgroundColor: '#fff' }]}>
                {isSaving ? (
                  <ActivityIndicator size="small" color={GoGoColors.primary} />
                ) : (
                  <Text style={[styles.saveButtonText, { color: GoGoColors.primary }]}>Save</Text>
                )}
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Branding Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(100)}>
          <Text style={styles.sectionTitle}>Branding</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Platform Name</Text>
            <TextInput
              style={styles.textInput}
              value={platformName}
              onChangeText={setPlatformName}
              placeholder="Platform Name"
              placeholderTextColor={GoGoColors.textMuted}
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Logo</Text>
            <TouchableOpacity style={styles.logoButton} onPress={handleLogoSelect}>
              {logo ? (
                <Image source={{ uri: logo }} style={styles.logoImage} />
              ) : (
                <View style={styles.logoPlaceholder}>
                  <Ionicons name="image" size={32} color={GoGoColors.textMuted} />
                  <Text style={styles.logoText}>Select Logo</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>
        {/* Monetization Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(200)}>
          <Text style={styles.sectionTitle}>Monetization</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Commission Rate (%)</Text>
            <TextInput
              style={styles.textInput}
              value={commissionRate}
              onChangeText={setCommissionRate}
              placeholder="20"
              placeholderTextColor={GoGoColors.textMuted}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Payout Threshold</Text>
            <TextInput
              style={styles.textInput}
              value={payoutThreshold}
              onChangeText={setPayoutThreshold}
              placeholder="10000"
              placeholderTextColor={GoGoColors.textMuted}
              keyboardType="numeric"
            />
          </View>
        </Animated.View>
        {/* Content Moderation Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(300)}>
          <Text style={styles.sectionTitle}>Content Moderation</Text>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Auto Moderation</Text>
            <Switch
              value={autoModeration}
              onValueChange={setAutoModeration}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={autoModeration ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Flag Threshold</Text>
            <TextInput
              style={styles.textInput}
              value={flagThreshold}
              onChangeText={setFlagThreshold}
              placeholder="3"
              placeholderTextColor={GoGoColors.textMuted}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Default Policy</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={defaultPolicy}
              onChangeText={setDefaultPolicy}
              placeholder="Enter default moderation policy"
              placeholderTextColor={GoGoColors.textMuted}
              multiline
              numberOfLines={3}
            />
          </View>
        </Animated.View>
        {/* User Management Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(400)}>
          <Text style={styles.sectionTitle}>User Management</Text>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Allow Registration</Text>
            <Switch
              value={allowRegistration}
              onValueChange={setAllowRegistration}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={allowRegistration ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Allow Creators</Text>
            <Switch
              value={allowCreators}
              onValueChange={setAllowCreators}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={allowCreators ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Allow Admins</Text>
            <Switch
              value={allowAdmins}
              onValueChange={setAllowAdmins}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={allowAdmins ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Banned Users</Text>
            <Text style={styles.bannedUsersText}>{bannedUsers} users</Text>
          </View>
        </Animated.View>
        {/* Notification Settings Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(500)}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Email Notifications</Text>
            <Switch
              value={emailNotif}
              onValueChange={setEmailNotif}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={emailNotif ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Push Notifications</Text>
            <Switch
              value={pushNotif}
              onValueChange={setPushNotif}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={pushNotif ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>System Notifications</Text>
            <Switch
              value={systemNotif}
              onValueChange={setSystemNotif}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={systemNotif ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
        </Animated.View>
        {/* Security Section */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(600)}>
          <Text style={styles.sectionTitle}>Security</Text>
          <View style={styles.toggleRow}>
            <Text style={styles.toggleLabel}>Require 2FA</Text>
            <Switch
              value={require2FA}
              onValueChange={setRequire2FA}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={require2FA ? '#FFFFFF' : GoGoColors.textMuted}
            />
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password Policy</Text>
            <TextInput
              style={styles.textInput}
              value={passwordPolicy}
              onChangeText={setPasswordPolicy}
              placeholder="Password policy"
              placeholderTextColor={GoGoColors.textMuted}
            />
          </View>
        </Animated.View>
      </ScrollView>
      {/* Logo Modal */}
      <Modal
        visible={showLogoModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLogoModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Preview Logo</Text>
            {tempLogo && (
              <Image source={{ uri: tempLogo }} style={styles.logoPreview} />
            )}
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.modalButton} onPress={() => setShowLogoModal(false)}>
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.modalButton, styles.modalButtonPrimary]} onPress={handleSaveLogo}>
                <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerGradient: {
    paddingTop: 40,
    paddingBottom: 12,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  saveButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: GoGoColors.primary,
    fontWeight: '700',
    fontSize: 14,
  },
  content: {
    flex: 1,
    marginTop: 90,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: GoGoColors.textPrimary,
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  logoButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: GoGoColors.border,
  },
  logoImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  logoPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontSize: 14,
    color: GoGoColors.textMuted,
    marginTop: 8,
  },
  toggleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  toggleLabel: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
  },
  bannedUsersText: {
    fontSize: 16,
    color: GoGoColors.error,
    fontWeight: 'bold',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  logoPreview: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignSelf: 'center',
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
  },
  modalButtonPrimary: {
    backgroundColor: GoGoColors.primary,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
  },
  modalButtonTextPrimary: {
    color: '#FFFFFF',
  },
}); 