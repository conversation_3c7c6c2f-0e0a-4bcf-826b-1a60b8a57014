import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  FadeIn, 
  SlideInDown, 
  SlideInRight,
} from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

const { width } = Dimensions.get('window');

interface Props {
  onClose: () => void;
}

// Sample data - replace with real data from databaseService
const reportsData = [
  {
    id: '1',
    type: 'Content',
    title: 'Inappropriate Video Content',
    description: 'Video contains inappropriate language and content',
    reportedBy: 'Alice Banda',
    reportedUser: '<PERSON>',
    contentId: 'video_123',
    contentTitle: 'Comedy Skit Gone Wrong',
    category: 'Inappropriate Content',
    status: 'Pending',
    priority: 'High',
    createdAt: '2024-01-14T10:30:00Z',
    evidence: ['Screenshot 1', 'Screenshot 2'],
  },
  {
    id: '2',
    type: 'User',
    title: 'Harassment in Comments',
    description: 'User is posting offensive comments and harassing other users',
    reportedBy: 'Grace Mwale',
    reportedUser: 'Peter Nyirenda',
    contentId: 'comment_456',
    contentTitle: 'Comment on "Music Video"',
    category: 'Harassment',
    status: 'Under Review',
    priority: 'Medium',
    createdAt: '2024-01-13T15:45:00Z',
    evidence: ['Comment screenshots'],
  },
  {
    id: '3',
    type: 'Technical',
    title: 'Video Upload Failure',
    description: 'Users reporting video upload failures on mobile app',
    reportedBy: 'System',
    reportedUser: 'Multiple Users',
    contentId: 'system_001',
    contentTitle: 'Upload System Error',
    category: 'Technical Issue',
    status: 'Resolved',
    priority: 'High',
    createdAt: '2024-01-12T09:15:00Z',
    evidence: ['Error logs', 'User reports'],
  },
  {
    id: '4',
    type: 'Payment',
    title: 'Payment Not Processed',
    description: 'User paid for subscription but access not granted',
    reportedBy: 'David Kachali',
    reportedUser: 'System',
    contentId: 'payment_789',
    contentTitle: 'Subscription Payment Issue',
    category: 'Payment Issue',
    status: 'Pending',
    priority: 'High',
    createdAt: '2024-01-14T08:20:00Z',
    evidence: ['Payment receipt', 'Account screenshot'],
  },
  {
    id: '5',
    type: 'Content',
    title: 'Copyright Violation',
    description: 'Video appears to contain copyrighted music without permission',
    reportedBy: 'Sarah Mvula',
    reportedUser: 'Moses Tembo',
    contentId: 'video_567',
    contentTitle: 'Dance Video with Popular Song',
    category: 'Copyright',
    status: 'Under Review',
    priority: 'Medium',
    createdAt: '2024-01-11T14:30:00Z',
    evidence: ['Original content link', 'Comparison video'],
  },
];

const statusColors = {
  Pending: '#F59E0B',
  'Under Review': '#3B82F6',
  Resolved: '#10B981',
  Rejected: '#EF4444',
  Escalated: '#8B5CF6',
};

const priorityColors = {
  Low: '#6B7280',
  Medium: '#F59E0B',
  High: '#EF4444',
  Critical: '#7C2D12',
};

const categoryColors = {
  'Inappropriate Content': '#EF4444',
  'Harassment': '#DC2626',
  'Technical Issue': '#3B82F6',
  'Payment Issue': '#F59E0B',
  'Copyright': '#8B5CF6',
  'Spam': '#6B7280',
  'Other': '#9CA3AF',
};

// Helper functions
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'Content': return 'videocam';
    case 'User': return 'person';
    case 'Technical': return 'bug';
    case 'Payment': return 'card';
    default: return 'alert-circle';
  }
};

// Modern Stats Card Component
const StatsCard = ({
  title,
  value,
  icon,
  color,
  index,
  trend
}: {
  title: string;
  value: string;
  icon: string;
  color: string;
  index: number;
  trend?: string;
}) => (
  <Animated.View
    style={styles.modernStatsCard}
    entering={SlideInDown.delay(index * 100)}
  >
    <View style={styles.statsCardHeader}>
      <View style={[styles.modernStatsIcon, { backgroundColor: color }]}>
        <Ionicons name={icon as any} size={20} color="#FFFFFF" />
      </View>
      {trend && (
        <View style={[styles.trendBadge, { backgroundColor: trend.startsWith('+') ? '#DCFCE7' : '#FEF2F2' }]}>
          <Text style={[styles.trendText, { color: trend.startsWith('+') ? '#16A34A' : '#DC2626' }]}>
            {trend}
          </Text>
        </View>
      )}
    </View>
    <Text style={styles.modernStatsValue}>{value}</Text>
    <Text style={styles.modernStatsTitle}>{title}</Text>
  </Animated.View>
);

// Modern Report Card Component
const ReportCard = ({
  report,
  index,
  onAction
}: {
  report: any;
  index: number;
  onAction: (reportId: string, action: string) => void;
}) => (
  <Animated.View
    style={styles.modernReportCard}
    entering={SlideInRight.delay(index * 50)}
  >
    {/* Card Header with Status Indicator */}
    <View style={styles.cardTopBar}>
      <View style={[styles.statusIndicator, { backgroundColor: statusColors[report.status] }]} />
      <View style={styles.cardHeaderContent}>
        <View style={styles.cardHeaderLeft}>
          <View style={[styles.modernTypeIcon, { backgroundColor: categoryColors[report.category] }]}>
            <Ionicons
              name={getTypeIcon(report.type) as any}
              size={16}
              color="#FFFFFF"
            />
          </View>
          <View style={styles.cardTitleSection}>
            <Text style={styles.modernReportTitle}>{report.title}</Text>
            <Text style={styles.modernReportCategory}>{report.category}</Text>
          </View>
        </View>
        <View style={styles.cardHeaderRight}>
          <View style={[styles.modernPriorityBadge, { backgroundColor: priorityColors[report.priority] }]}>
            <Text style={styles.modernPriorityText}>{report.priority}</Text>
          </View>
        </View>
      </View>
    </View>

    {/* Report Content */}
    <View style={styles.cardContent}>
      <Text style={styles.modernReportDescription}>{report.description}</Text>

      {/* Info Grid */}
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <Ionicons name="person-outline" size={14} color="#6B7280" />
          <Text style={styles.infoLabel}>Reporter</Text>
          <Text style={styles.infoValue}>{report.reportedBy}</Text>
        </View>
        <View style={styles.infoItem}>
          <Ionicons name="flag-outline" size={14} color="#6B7280" />
          <Text style={styles.infoLabel}>Target</Text>
          <Text style={styles.infoValue}>{report.reportedUser}</Text>
        </View>
        <View style={styles.infoItem}>
          <Ionicons name="videocam-outline" size={14} color="#6B7280" />
          <Text style={styles.infoLabel}>Content</Text>
          <Text style={styles.infoValue} numberOfLines={1}>{report.contentTitle}</Text>
        </View>
        <View style={styles.infoItem}>
          <Ionicons name="time-outline" size={14} color="#6B7280" />
          <Text style={styles.infoLabel}>Date</Text>
          <Text style={styles.infoValue}>{formatDate(report.createdAt)}</Text>
        </View>
      </View>

      {/* Evidence Section */}
      {report.evidence && report.evidence.length > 0 && (
        <View style={styles.evidenceSection}>
          <Text style={styles.evidenceTitle}>Evidence ({report.evidence.length})</Text>
          <View style={styles.evidenceTags}>
            {report.evidence.slice(0, 2).map((evidence: string, idx: number) => (
              <View key={idx} style={styles.evidenceTag}>
                <Ionicons name="document-outline" size={12} color="#6B7280" />
                <Text style={styles.evidenceText}>{evidence}</Text>
              </View>
            ))}
            {report.evidence.length > 2 && (
              <View style={styles.evidenceTag}>
                <Text style={styles.evidenceText}>+{report.evidence.length - 2} more</Text>
              </View>
            )}
          </View>
        </View>
      )}
    </View>

    {/* Action Buttons */}
    {report.status === 'Pending' && (
      <View style={styles.modernActionButtons}>
        <TouchableOpacity
          style={[styles.modernActionButton, styles.reviewButton]}
          onPress={() => onAction(report.id, 'review')}
        >
          <Ionicons name="eye" size={16} color="#3B82F6" />
          <Text style={[styles.modernActionText, { color: '#3B82F6' }]}>Review</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.modernActionButton, styles.resolveButton]}
          onPress={() => onAction(report.id, 'resolve')}
        >
          <Ionicons name="checkmark-circle" size={16} color="#10B981" />
          <Text style={[styles.modernActionText, { color: '#10B981' }]}>Resolve</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.modernActionButton, styles.escalateButton]}
          onPress={() => onAction(report.id, 'escalate')}
        >
          <Ionicons name="trending-up" size={16} color="#F59E0B" />
          <Text style={[styles.modernActionText, { color: '#F59E0B' }]}>Escalate</Text>
        </TouchableOpacity>
      </View>
    )}

    {/* Status Badge for Non-Pending Reports */}
    {report.status !== 'Pending' && (
      <View style={styles.statusFooter}>
        <View style={[styles.modernStatusBadge, { backgroundColor: statusColors[report.status] + '15' }]}>
          <Ionicons
            name={report.status === 'Resolved' ? 'checkmark-circle' :
                  report.status === 'Under Review' ? 'eye' : 'alert-circle'}
            size={14}
            color={statusColors[report.status]}
          />
          <Text style={[styles.modernStatusText, { color: statusColors[report.status] }]}>
            {report.status}
          </Text>
        </View>
      </View>
    )}
  </Animated.View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F1F5F9',
  },
  // Modern Header
  modernHeader: {
    paddingTop: 44,
    paddingBottom: 0,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  modernHeaderTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  modernHeaderSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  // Modern Stats Grid
  modernStatsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingTop: 24,
    gap: 12,
  },
  modernStatsCard: {
    width: (width - 56) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  statsCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  modernStatsIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trendBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  modernStatsValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1E293B',
    marginBottom: 4,
  },
  modernStatsTitle: {
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500',
  },
  // Quick Actions Bar
  quickActionsBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  quickAction: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionCount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1E293B',
  },
  // Modern Filter Tabs
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 6,
    marginHorizontal: 16,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  filterButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#1E293B',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  // Reports Container
  reportsContainer: {
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1E293B',
  },
  sectionCount: {
    fontSize: 14,
    color: '#64748B',
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    fontWeight: '600',
  },
  // Modern Report Card
  modernReportCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    overflow: 'hidden',
  },
  cardTopBar: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statusIndicator: {
    width: 4,
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
  },
  cardHeaderContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingBottom: 16,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  modernTypeIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  cardTitleSection: {
    flex: 1,
  },
  modernReportTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
    lineHeight: 22,
  },
  modernReportCategory: {
    fontSize: 13,
    color: '#64748B',
    fontWeight: '500',
  },
  cardHeaderRight: {
    alignItems: 'flex-end',
  },
  modernPriorityBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  modernPriorityText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  // Card Content
  cardContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modernReportDescription: {
    fontSize: 15,
    color: '#475569',
    lineHeight: 22,
    marginBottom: 16,
  },
  // Info Grid
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 12,
  },
  infoLabel: {
    fontSize: 11,
    color: '#64748B',
    fontWeight: '600',
    marginTop: 4,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 13,
    color: '#1E293B',
    fontWeight: '600',
  },
  // Evidence Section
  evidenceSection: {
    marginBottom: 16,
  },
  evidenceTitle: {
    fontSize: 13,
    color: '#64748B',
    fontWeight: '600',
    marginBottom: 8,
  },
  evidenceTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  evidenceTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  evidenceText: {
    fontSize: 12,
    color: '#64748B',
    fontWeight: '500',
  },
  // Modern Action Buttons
  modernActionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  modernActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 6,
    borderWidth: 1,
  },
  reviewButton: {
    backgroundColor: '#EFF6FF',
    borderColor: '#DBEAFE',
  },
  resolveButton: {
    backgroundColor: '#F0FDF4',
    borderColor: '#DCFCE7',
  },
  escalateButton: {
    backgroundColor: '#FFFBEB',
    borderColor: '#FEF3C7',
  },
  modernActionText: {
    fontSize: 13,
    fontWeight: '600',
  },
  // Status Footer
  statusFooter: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modernStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    gap: 6,
  },
  modernStatusText: {
    fontSize: 13,
    fontWeight: '600',
  },

  // Modern Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1E293B',
    marginTop: 20,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 15,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomSpacing: {
    height: 120,
  },
});

export default function ReportsIssuesScreen({ onClose }: Props) {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'review' | 'resolved'>('all');

  const onRefresh = async () => {
    setRefreshing(true);
    hapticFeedback.light();
    setTimeout(() => setRefreshing(false), 2000);
  };

  const handleReportAction = (reportId: string, action: string) => {
    hapticFeedback.light();
    
    switch (action) {
      case 'review':
        Alert.alert('Review Report', `Report ${reportId} moved to review status.`);
        break;
      case 'resolve':
        Alert.alert('Resolve Report', `Report ${reportId} has been resolved.`);
        break;
      case 'escalate':
        Alert.alert('Escalate Report', `Report ${reportId} has been escalated to senior admin.`);
        break;
    }
  };

  const filteredReports = reportsData.filter(report => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'pending') return report.status === 'Pending';
    if (selectedFilter === 'review') return report.status === 'Under Review';
    if (selectedFilter === 'resolved') return report.status === 'Resolved';
    return true;
  });

  const stats = {
    total: reportsData.length,
    pending: reportsData.filter(r => r.status === 'Pending').length,
    review: reportsData.filter(r => r.status === 'Under Review').length,
    resolved: reportsData.filter(r => r.status === 'Resolved').length,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Modern Header */}
      <Animated.View style={styles.modernHeader} entering={FadeIn}>
        <LinearGradient
          colors={['#1E293B', '#334155']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.modernHeaderTitle}>Reports & Issues</Text>
              <Text style={styles.modernHeaderSubtitle}>Content Moderation Dashboard</Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.headerActionButton} onPress={onRefresh}>
                <Ionicons name="refresh" size={18} color="#FFFFFF" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerActionButton}>
                <Ionicons name="filter" size={18} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

      <ScrollView 
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Modern Stats Grid */}
        <View style={styles.modernStatsGrid}>
          <StatsCard
            title="Total Reports"
            value={stats.total.toString()}
            icon="document-text"
            color="#6366F1"
            index={0}
            trend="+12%"
          />
          <StatsCard
            title="Pending Review"
            value={stats.pending.toString()}
            icon="clock"
            color="#F59E0B"
            index={1}
            trend="-8%"
          />
          <StatsCard
            title="Under Review"
            value={stats.review.toString()}
            icon="eye"
            color="#3B82F6"
            index={2}
            trend="+5%"
          />
          <StatsCard
            title="Resolved Today"
            value={stats.resolved.toString()}
            icon="checkmark-circle"
            color="#10B981"
            index={3}
            trend="+23%"
          />
        </View>

        {/* Quick Actions Bar */}
        <Animated.View style={styles.quickActionsBar} entering={SlideInDown.delay(400)}>
          <TouchableOpacity style={styles.quickAction}>
            <View style={[styles.quickActionIcon, { backgroundColor: '#EF4444' }]}>
              <Ionicons name="alert" size={16} color="#FFFFFF" />
            </View>
            <Text style={styles.quickActionText}>High Priority</Text>
            <Text style={styles.quickActionCount}>2</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickAction}>
            <View style={[styles.quickActionIcon, { backgroundColor: '#8B5CF6' }]}>
              <Ionicons name="flag" size={16} color="#FFFFFF" />
            </View>
            <Text style={styles.quickActionText}>Content Issues</Text>
            <Text style={styles.quickActionCount}>3</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickAction}>
            <View style={[styles.quickActionIcon, { backgroundColor: '#F59E0B' }]}>
              <Ionicons name="card" size={16} color="#FFFFFF" />
            </View>
            <Text style={styles.quickActionText}>Payment Issues</Text>
            <Text style={styles.quickActionCount}>1</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Filter Tabs */}
        <Animated.View style={styles.filterContainer} entering={SlideInDown.delay(400)}>
          {(['all', 'pending', 'review', 'resolved'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => {
                setSelectedFilter(filter);
                hapticFeedback.light();
              }}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Reports List */}
        <View style={styles.reportsContainer}>
          <Animated.View style={styles.sectionHeader} entering={SlideInDown.delay(500)}>
            <Text style={styles.sectionTitle}>
              {selectedFilter === 'all' ? 'All Reports' : 
               selectedFilter === 'pending' ? 'Pending Reports' :
               selectedFilter === 'review' ? 'Under Review' : 'Resolved Reports'}
            </Text>
            <Text style={styles.sectionCount}>
              {filteredReports.length} {filteredReports.length === 1 ? 'report' : 'reports'}
            </Text>
          </Animated.View>

          {filteredReports.map((report, index) => (
            <ReportCard
              key={report.id}
              report={report}
              index={index}
              onAction={handleReportAction}
            />
          ))}

          {filteredReports.length === 0 && (
            <Animated.View style={styles.emptyState} entering={FadeIn.delay(600)}>
              <Ionicons name="document-outline" size={48} color="#9CA3AF" />
              <Text style={styles.emptyTitle}>No Reports Found</Text>
              <Text style={styles.emptyText}>
                {selectedFilter === 'all' 
                  ? 'No reports have been submitted yet.'
                  : `No ${selectedFilter} reports at this time.`
                }
              </Text>
            </Animated.View>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}
