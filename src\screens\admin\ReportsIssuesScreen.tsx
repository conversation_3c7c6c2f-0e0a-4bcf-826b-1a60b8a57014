import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  ScrollView,
  Dimensions,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  FadeIn, 
  SlideInDown, 
  SlideInRight,
} from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

const { width } = Dimensions.get('window');

interface Props {
  onClose: () => void;
}

// Sample data - replace with real data from databaseService
const reportsData = [
  {
    id: '1',
    type: 'Content',
    title: 'Inappropriate Video Content',
    description: 'Video contains inappropriate language and content',
    reportedBy: 'Alice Banda',
    reportedUser: '<PERSON>',
    contentId: 'video_123',
    contentTitle: 'Comedy Skit Gone Wrong',
    category: 'Inappropriate Content',
    status: 'Pending',
    priority: 'High',
    createdAt: '2024-01-14T10:30:00Z',
    evidence: ['Screenshot 1', 'Screenshot 2'],
  },
  {
    id: '2',
    type: 'User',
    title: 'Harassment in Comments',
    description: 'User is posting offensive comments and harassing other users',
    reportedBy: 'Grace Mwale',
    reportedUser: 'Peter Nyirenda',
    contentId: 'comment_456',
    contentTitle: 'Comment on "Music Video"',
    category: 'Harassment',
    status: 'Under Review',
    priority: 'Medium',
    createdAt: '2024-01-13T15:45:00Z',
    evidence: ['Comment screenshots'],
  },
  {
    id: '3',
    type: 'Technical',
    title: 'Video Upload Failure',
    description: 'Users reporting video upload failures on mobile app',
    reportedBy: 'System',
    reportedUser: 'Multiple Users',
    contentId: 'system_001',
    contentTitle: 'Upload System Error',
    category: 'Technical Issue',
    status: 'Resolved',
    priority: 'High',
    createdAt: '2024-01-12T09:15:00Z',
    evidence: ['Error logs', 'User reports'],
  },
  {
    id: '4',
    type: 'Payment',
    title: 'Payment Not Processed',
    description: 'User paid for subscription but access not granted',
    reportedBy: 'David Kachali',
    reportedUser: 'System',
    contentId: 'payment_789',
    contentTitle: 'Subscription Payment Issue',
    category: 'Payment Issue',
    status: 'Pending',
    priority: 'High',
    createdAt: '2024-01-14T08:20:00Z',
    evidence: ['Payment receipt', 'Account screenshot'],
  },
  {
    id: '5',
    type: 'Content',
    title: 'Copyright Violation',
    description: 'Video appears to contain copyrighted music without permission',
    reportedBy: 'Sarah Mvula',
    reportedUser: 'Moses Tembo',
    contentId: 'video_567',
    contentTitle: 'Dance Video with Popular Song',
    category: 'Copyright',
    status: 'Under Review',
    priority: 'Medium',
    createdAt: '2024-01-11T14:30:00Z',
    evidence: ['Original content link', 'Comparison video'],
  },
];

const statusColors = {
  Pending: '#F59E0B',
  'Under Review': '#3B82F6',
  Resolved: '#10B981',
  Rejected: '#EF4444',
  Escalated: '#8B5CF6',
};

const priorityColors = {
  Low: '#6B7280',
  Medium: '#F59E0B',
  High: '#EF4444',
  Critical: '#7C2D12',
};

const categoryColors = {
  'Inappropriate Content': '#EF4444',
  'Harassment': '#DC2626',
  'Technical Issue': '#3B82F6',
  'Payment Issue': '#F59E0B',
  'Copyright': '#8B5CF6',
  'Spam': '#6B7280',
  'Other': '#9CA3AF',
};

// Helper functions
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'Content': return 'videocam';
    case 'User': return 'person';
    case 'Technical': return 'bug';
    case 'Payment': return 'card';
    default: return 'alert-circle';
  }
};

// Stats Card Component
const StatsCard = ({ 
  title, 
  value, 
  icon, 
  color, 
  index 
}: {
  title: string;
  value: string;
  icon: string;
  color: string;
  index: number;
}) => (
  <Animated.View 
    style={[styles.statsCard]}
    entering={SlideInDown.delay(index * 100)}
  >
    <LinearGradient
      colors={[color + '15', color + '05']}
      style={styles.statsGradient}
    >
      <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <Text style={styles.statsTitle}>{title}</Text>
      <Text style={styles.statsValue}>{value}</Text>
    </LinearGradient>
  </Animated.View>
);

// Report Item Component
const ReportItem = ({ 
  report, 
  index, 
  onAction 
}: { 
  report: any; 
  index: number;
  onAction: (reportId: string, action: string) => void;
}) => (
  <Animated.View 
    style={styles.reportItem}
    entering={SlideInRight.delay(index * 50)}
  >
    <View style={styles.reportHeader}>
      <View style={styles.reportLeft}>
        <View style={[styles.reportTypeIcon, { backgroundColor: categoryColors[report.category] + '20' }]}>
          <Ionicons 
            name={getTypeIcon(report.type) as any} 
            size={20} 
            color={categoryColors[report.category]} 
          />
        </View>
        <View style={styles.reportInfo}>
          <Text style={styles.reportTitle}>{report.title}</Text>
          <Text style={styles.reportCategory}>{report.category}</Text>
        </View>
      </View>
      <View style={styles.reportRight}>
        <View style={[styles.priorityBadge, { backgroundColor: priorityColors[report.priority] + '20' }]}>
          <Text style={[styles.priorityText, { color: priorityColors[report.priority] }]}>
            {report.priority}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: statusColors[report.status] + '20' }]}>
          <Text style={[styles.statusText, { color: statusColors[report.status] }]}>
            {report.status}
          </Text>
        </View>
      </View>
    </View>

    <Text style={styles.reportDescription}>{report.description}</Text>
    
    <View style={styles.reportDetails}>
      <Text style={styles.reportDetailText}>
        <Text style={styles.reportDetailLabel}>Reported by: </Text>
        {report.reportedBy}
      </Text>
      <Text style={styles.reportDetailText}>
        <Text style={styles.reportDetailLabel}>Target: </Text>
        {report.reportedUser}
      </Text>
      <Text style={styles.reportDetailText}>
        <Text style={styles.reportDetailLabel}>Content: </Text>
        {report.contentTitle}
      </Text>
      <Text style={styles.reportDetailText}>
        <Text style={styles.reportDetailLabel}>Date: </Text>
        {formatDate(report.createdAt)}
      </Text>
    </View>

    {report.status === 'Pending' && (
      <View style={styles.reportActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => onAction(report.id, 'review')}
        >
          <Ionicons name="eye" size={16} color="#3B82F6" />
          <Text style={[styles.actionButtonText, { color: '#3B82F6' }]}>Review</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => onAction(report.id, 'resolve')}
        >
          <Ionicons name="checkmark" size={16} color="#10B981" />
          <Text style={[styles.actionButtonText, { color: '#10B981' }]}>Resolve</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => onAction(report.id, 'escalate')}
        >
          <Ionicons name="arrow-up" size={16} color="#8B5CF6" />
          <Text style={[styles.actionButtonText, { color: '#8B5CF6' }]}>Escalate</Text>
        </TouchableOpacity>
      </View>
    )}
  </Animated.View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 44,
    paddingBottom: 0,
  },
  headerGradient: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  // Stats Cards
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingTop: 20,
    gap: 12,
  },
  statsCard: {
    width: (width - 56) / 2,
    marginBottom: 12,
  },
  statsGradient: {
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  statsIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statsTitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    textAlign: 'center',
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  // Filter Tabs
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 20,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#DC2626',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  // Reports Container
  reportsContainer: {
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  sectionCount: {
    fontSize: 14,
    color: '#6B7280',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  // Report Item
  reportItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  reportLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  reportTypeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  reportInfo: {
    flex: 1,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  reportCategory: {
    fontSize: 14,
    color: '#6B7280',
  },
  reportRight: {
    alignItems: 'flex-end',
    gap: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  reportDescription: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginBottom: 12,
  },
  reportDetails: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  reportDetailText: {
    fontSize: 13,
    color: '#6B7280',
    marginBottom: 4,
  },
  reportDetailLabel: {
    fontWeight: '600',
    color: '#374151',
  },
  reportActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    gap: 6,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default function ReportsIssuesScreen({ onClose }: Props) {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'review' | 'resolved'>('all');

  const onRefresh = async () => {
    setRefreshing(true);
    hapticFeedback.light();
    setTimeout(() => setRefreshing(false), 2000);
  };

  const handleReportAction = (reportId: string, action: string) => {
    hapticFeedback.light();
    
    switch (action) {
      case 'review':
        Alert.alert('Review Report', `Report ${reportId} moved to review status.`);
        break;
      case 'resolve':
        Alert.alert('Resolve Report', `Report ${reportId} has been resolved.`);
        break;
      case 'escalate':
        Alert.alert('Escalate Report', `Report ${reportId} has been escalated to senior admin.`);
        break;
    }
  };

  const filteredReports = reportsData.filter(report => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'pending') return report.status === 'Pending';
    if (selectedFilter === 'review') return report.status === 'Under Review';
    if (selectedFilter === 'resolved') return report.status === 'Resolved';
    return true;
  });

  const stats = {
    total: reportsData.length,
    pending: reportsData.filter(r => r.status === 'Pending').length,
    review: reportsData.filter(r => r.status === 'Under Review').length,
    resolved: reportsData.filter(r => r.status === 'Resolved').length,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <LinearGradient
          colors={['#DC2626', '#B91C1C']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Reports & Issues</Text>
              <Text style={styles.headerSubtitle}>Content Moderation & Support</Text>
            </View>
            <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
              <Ionicons name="refresh" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>

      <ScrollView 
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <StatsCard
            title="Total Reports"
            value={stats.total.toString()}
            icon="document-text"
            color="#6B7280"
            index={0}
          />
          <StatsCard
            title="Pending"
            value={stats.pending.toString()}
            icon="time"
            color="#F59E0B"
            index={1}
          />
          <StatsCard
            title="Under Review"
            value={stats.review.toString()}
            icon="eye"
            color="#3B82F6"
            index={2}
          />
          <StatsCard
            title="Resolved"
            value={stats.resolved.toString()}
            icon="checkmark-circle"
            color="#10B981"
            index={3}
          />
        </View>

        {/* Filter Tabs */}
        <Animated.View style={styles.filterContainer} entering={SlideInDown.delay(400)}>
          {(['all', 'pending', 'review', 'resolved'] as const).map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterButton,
                selectedFilter === filter && styles.filterButtonActive
              ]}
              onPress={() => {
                setSelectedFilter(filter);
                hapticFeedback.light();
              }}
            >
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.filterButtonTextActive
              ]}>
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Reports List */}
        <View style={styles.reportsContainer}>
          <Animated.View style={styles.sectionHeader} entering={SlideInDown.delay(500)}>
            <Text style={styles.sectionTitle}>
              {selectedFilter === 'all' ? 'All Reports' : 
               selectedFilter === 'pending' ? 'Pending Reports' :
               selectedFilter === 'review' ? 'Under Review' : 'Resolved Reports'}
            </Text>
            <Text style={styles.sectionCount}>
              {filteredReports.length} {filteredReports.length === 1 ? 'report' : 'reports'}
            </Text>
          </Animated.View>

          {filteredReports.map((report, index) => (
            <ReportItem
              key={report.id}
              report={report}
              index={index}
              onAction={handleReportAction}
            />
          ))}

          {filteredReports.length === 0 && (
            <Animated.View style={styles.emptyState} entering={FadeIn.delay(600)}>
              <Ionicons name="document-outline" size={48} color="#9CA3AF" />
              <Text style={styles.emptyTitle}>No Reports Found</Text>
              <Text style={styles.emptyText}>
                {selectedFilter === 'all' 
                  ? 'No reports have been submitted yet.'
                  : `No ${selectedFilter} reports at this time.`
                }
              </Text>
            </Animated.View>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}
