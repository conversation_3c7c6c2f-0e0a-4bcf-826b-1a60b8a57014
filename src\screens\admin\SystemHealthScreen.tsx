import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

const mockHealth = {
  serverStatus: 'Online',
  uptime: '14 days 3h',
  apiLatency: 120, // ms
  errorRate: 0.2, // %
  cpu: 38, // %
  memory: 62, // %
  storage: 71, // %
  lastChecked: '2024-06-07T10:30:00Z',
};

const statusColors = {
  Online: GoGoColors.success,
  Offline: GoGoColors.error,
  Degraded: GoGoColors.warning,
};

interface Props {
  onClose: () => void;
}

export default function SystemHealthScreen({ onClose }: Props) {
  const HEADER_HEIGHT = 100;
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>System Health</Text>
              <Text style={styles.headerSubtitle}>Monitor server and API status</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      <View style={[styles.statsSection, { paddingTop: HEADER_HEIGHT }]}> 
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: statusColors[mockHealth.serverStatus] + '10' }]}> 
            <Ionicons name="server" size={28} color={statusColors[mockHealth.serverStatus]} />
            <Text style={styles.statsLabel}>Server Status</Text>
            <Text style={styles.statsValue}>{mockHealth.serverStatus}</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.primary + '10' }]}> 
            <Ionicons name="time" size={28} color={GoGoColors.primary} />
            <Text style={styles.statsLabel}>Uptime</Text>
            <Text style={styles.statsValue}>{mockHealth.uptime}</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.info + '10' }]}> 
            <Ionicons name="pulse" size={28} color={GoGoColors.info} />
            <Text style={styles.statsLabel}>API Latency</Text>
            <Text style={styles.statsValue}>{mockHealth.apiLatency} ms</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.error + '10' }]}> 
            <Ionicons name="alert-circle" size={28} color={GoGoColors.error} />
            <Text style={styles.statsLabel}>Error Rate</Text>
            <Text style={styles.statsValue}>{mockHealth.errorRate}%</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.warning + '10' }]}> 
            <Ionicons name="speedometer" size={28} color={GoGoColors.warning} />
            <Text style={styles.statsLabel}>CPU Usage</Text>
            <Text style={styles.statsValue}>{mockHealth.cpu}%</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.success + '10' }]}> 
            <Ionicons name="bar-chart" size={28} color={GoGoColors.success} />
            <Text style={styles.statsLabel}>Memory Usage</Text>
            <Text style={styles.statsValue}>{mockHealth.memory}%</Text>
          </View>
        </View>
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.primaryAccent + '10' }]}> 
            <Ionicons name="cloud-outline" size={28} color={GoGoColors.primaryAccent} />
            <Text style={styles.statsLabel}>Storage Usage</Text>
            <Text style={styles.statsValue}>{mockHealth.storage}%</Text>
          </View>
        </View>
        <Text style={styles.lastChecked}>Last checked: {new Date(mockHealth.lastChecked).toLocaleString('en-GB')}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statsSection: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  statsCard: {
    flex: 1,
    borderRadius: 16,
    padding: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  statsLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginTop: 8,
    marginBottom: 2,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  lastChecked: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    marginTop: 12,
    textAlign: 'center',
  },
}); 