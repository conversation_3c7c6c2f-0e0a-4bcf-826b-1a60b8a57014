import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  StatusBar,
  FlatList,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';
import { hapticFeedback } from '../../utils/animations';

// Mock user data
const mockUsers = [
  {
    id: '1',
    username: 'user123',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    userType: 'user',
    status: 'Active',
    joined: '2024-01-10',
  },
  {
    id: '2',
    username: 'creatorA',
    fullName: 'Bob Creator',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    userType: 'creator',
    status: 'Active',
    joined: '2024-02-15',
  },
  {
    id: '3',
    username: 'admin<PERSON><PERSON>',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/3.jpg',
    userType: 'admin',
    status: 'Banned',
    joined: '2023-12-01',
  },
  {
    id: '4',
    username: 'user789',
    fullName: 'Charlie Smith',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    userType: 'user',
    status: 'Deactivated',
    joined: '2024-03-20',
  },
];

const statusColors = {
  Active: GoGoColors.success,
  Banned: GoGoColors.error,
  Deactivated: GoGoColors.warning,
};

interface Props {
  onClose: () => void;
}

export default function UserManagementScreen({ onClose }: Props) {
  const [search, setSearch] = useState('');
  const [filterType, setFilterType] = useState<'All' | 'user' | 'creator' | 'admin'>('All');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);

  const filteredUsers = mockUsers.filter((user) => {
    const matchesType = filterType === 'All' || user.userType === filterType;
    const matchesSearch =
      user.username.toLowerCase().includes(search.toLowerCase()) ||
      user.fullName.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  const handleAction = async (action: string) => {
    setIsActionLoading(true);
    hapticFeedback.light();
    setTimeout(() => {
      setIsActionLoading(false);
      setSelectedUser(null);
      hapticFeedback.success();
    }, 1000);
  };

  // Height of the header (nav) including padding. Adjust if you change header size.
  const HEADER_HEIGHT = 100;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>User Management</Text>
              <Text style={styles.headerSubtitle}>Manage users, creators, and admins</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      {/* Filters are now in ListHeaderComponent */}
      {/* Users List */}
      <FlatList
        data={filteredUsers}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[styles.listContent, { paddingTop: HEADER_HEIGHT }]}
        ListHeaderComponent={
          <View style={styles.filters}>
            <View style={styles.filterRow}>
              <View style={styles.typeFilterGroup}>
                {(['All', 'user', 'creator', 'admin'] as const).map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[styles.typeFilter, filterType === type && styles.typeFilterActive]}
                    onPress={() => setFilterType(type)}
                  >
                    <Text style={[styles.typeFilterText, filterType === type && styles.typeFilterTextActive]}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <View style={styles.searchBox}>
                <Ionicons name="search" size={18} color={GoGoColors.textMuted} />
                <TextInput
                  style={styles.searchInput}
                  value={search}
                  onChangeText={setSearch}
                  placeholder="Search users..."
                  placeholderTextColor={GoGoColors.textMuted}
                />
              </View>
            </View>
          </View>
        }
        renderItem={({ item, index }) => (
          <Animated.View entering={SlideInDown.delay(index * 80)}>
            <TouchableOpacity
              style={styles.userCard}
              onPress={() => setSelectedUser(item)}
              activeOpacity={0.85}
            >
              <Image source={{ uri: item.avatar }} style={styles.userAvatar} />
              <View style={styles.userCenter}>
                <Text style={styles.userName} numberOfLines={1}>{item.fullName}</Text>
                <Text style={styles.userUsername} numberOfLines={1}>@{item.username}</Text>
                <Text style={styles.userEmail} numberOfLines={1}>{item.email}</Text>
                <Text style={styles.userMeta}>Joined {item.joined}</Text>
              </View>
              <View style={styles.userRight}>
                <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                  <Text style={styles.statusBadgeText}>{item.status}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
              </View>
            </TouchableOpacity>
          </Animated.View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="person-circle-outline" size={48} color={GoGoColors.textMuted} />
            <Text style={styles.emptyText}>No users found.</Text>
          </View>
        }
      />
      {/* User Details Modal */}
      <Modal
        visible={!!selectedUser}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSelectedUser(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedUser && (
              <>
                <Image source={{ uri: selectedUser.avatar }} style={styles.modalAvatar} />
                <Text style={styles.modalTitle}>{selectedUser.fullName}</Text>
                <Text style={styles.modalSubtitle}>@{selectedUser.username} • {selectedUser.userType.charAt(0).toUpperCase() + selectedUser.userType.slice(1)}</Text>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Email</Text>
                  <Text style={styles.modalValue}>{selectedUser.email}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Status</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[selectedUser.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{selectedUser.status}</Text>
                  </View>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Joined</Text>
                  <Text style={styles.modalValue}>{selectedUser.joined}</Text>
                </View>
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.error }]}
                    onPress={() => handleAction('ban')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="ban" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Ban</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.primary }]}
                    onPress={() => handleAction('promote')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="arrow-up-circle" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Promote</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.warning }]}
                    onPress={() => handleAction('deactivate')}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="remove-circle" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Deactivate</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
                <TouchableOpacity style={styles.modalCloseButton} onPress={() => setSelectedUser(null)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  filters: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
  },
  typeFilterGroup: {
    flexDirection: 'row',
    gap: 6,
  },
  typeFilter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: GoGoColors.backgroundLight,
  },
  typeFilterActive: {
    backgroundColor: GoGoColors.primary,
  },
  typeFilterText: {
    fontSize: 13,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  typeFilterTextActive: {
    color: '#fff',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    flex: 1,
    marginLeft: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    color: GoGoColors.textPrimary,
    marginLeft: 8,
    paddingVertical: 8,
  },
  listContent: {
    paddingBottom: 40,
    paddingHorizontal: 10,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 14,
    backgroundColor: GoGoColors.backgroundLight,
  },
  userCenter: {
    flex: 1,
  },
  userName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  userUsername: {
    fontSize: 13,
    color: GoGoColors.primary,
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  userMeta: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  userRight: {
    alignItems: 'center',
    marginLeft: 10,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignSelf: 'center',
    marginBottom: 16,
    backgroundColor: GoGoColors.backgroundLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 16,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 12,
  },
  modalLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  modalValue: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 18,
    marginBottom: 8,
  },
  modalActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 16,
    marginBottom: 6,
  },
  modalActionText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  modalCloseButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 15,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
}); 