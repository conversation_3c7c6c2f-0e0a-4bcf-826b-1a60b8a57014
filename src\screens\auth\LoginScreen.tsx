import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  StatusBar,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { signIn, signInDemo, clearError } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';

import { hapticFeedback } from '../../utils/animations';

interface Props {
  onSwitchToRegister: () => void;
}

export default function LoginScreen({ onSwitchToRegister }: Props) {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [showDemoModal, setShowDemoModal] = useState(false);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    hapticFeedback.medium();

    try {
      await dispatch(signIn(formData)).unwrap();
      hapticFeedback.success();
    } catch (error: any) {
      hapticFeedback.error();
      Alert.alert('Login Failed', typeof error === 'string' ? error : error?.message || 'An error occurred');
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) {
      dispatch(clearError());
    }
  };

  const handleDemoLogin = (userType: 'user' | 'creator' | 'admin') => {
    setShowDemoModal(false);
    hapticFeedback.light();

    setTimeout(async () => {
      try {
        await dispatch(signInDemo(userType)).unwrap();
        hapticFeedback.success();
      } catch (error: any) {
        hapticFeedback.error();
        Alert.alert('Demo Login Failed', typeof error === 'string' ? error : error?.message || 'An error occurred');
      }
    }, 100);
  };

  const showDemoOptions = () => {
    hapticFeedback.light();
    setShowDemoModal(true);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Twitter-style Header */}
          <View style={styles.twitterHeader}>
            <View style={styles.twitterLogoContainer}>
              {/* GoGo Logo with Twitter-style design */}
              <View style={styles.twitterLogo}>
                <LinearGradient
                  colors={[GoGoColors.highlightGold, '#FFD700']}
                  style={styles.twitterLogoGradient}
                >
                  <Text style={styles.twitterLogoText}>GoGo</Text>
                </LinearGradient>
              </View>
            </View>
          </View>

          {/* Twitter-style Main Content */}
          <View style={styles.twitterMainContent}>
            {/* Twitter-style Welcome Text */}
            <View style={styles.twitterWelcomeSection}>
              <Text style={styles.twitterMainTitle}>Sign in to GoGo</Text>
            </View>

            {/* Twitter-style Form */}
            <View style={styles.twitterFormContainer}>
              {/* Email Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'email' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.email}
                    onChangeText={(text) => updateFormData('email', text)}
                    onFocus={() => {
                      setFocusedField('email');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'email' || formData.email) && styles.twitterInputLabelActive
                  ]}>
                    Email or username
                  </Text>
                </View>
              </View>

              {/* Password Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'password' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.password}
                    onChangeText={(text) => updateFormData('password', text)}
                    onFocus={() => {
                      setFocusedField('password');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'password' || formData.password) && styles.twitterInputLabelActive
                  ]}>
                    Password
                  </Text>
                  <TouchableOpacity
                    style={styles.twitterPasswordToggle}
                    onPress={() => {
                      setShowPassword(!showPassword);
                      hapticFeedback.light();
                    }}
                  >
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color="#536471"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Error Display - Twitter Style */}
              {error && (
                <View style={styles.twitterErrorContainer}>
                  <Text style={styles.twitterErrorText}>{error}</Text>
                </View>
              )}

              {/* Twitter-style Sign In Button */}
              <View>
                <TouchableOpacity
                  style={[styles.twitterLoginButton, isLoading && styles.twitterDisabledButton]}
                  onPress={handleLogin}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                  {isLoading ? (
                    <View style={styles.twitterLoadingContainer}>
                      <ActivityIndicator size="small" color="#FFFFFF" />
                      <Text style={styles.twitterButtonText}>Signing in...</Text>
                    </View>
                  ) : (
                    <Text style={styles.twitterButtonText}>Sign in</Text>
                  )}
                </TouchableOpacity>
              </View>

              {/* Twitter-style Forgot Password */}
              <View>
                <TouchableOpacity style={styles.twitterForgotPassword}>
                  <Text style={styles.twitterForgotPasswordText}>Forgot password?</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Twitter-style Divider */}
            <View style={styles.twitterDividerContainer}>
              <View style={styles.twitterDividerLine} />
              <Text style={styles.twitterDividerText}>or</Text>
              <View style={styles.twitterDividerLine} />
            </View>

            {/* Twitter-style Demo Button */}
            <View>
              <TouchableOpacity
                style={styles.twitterDemoButton}
                onPress={showDemoOptions}
                activeOpacity={0.8}
              >
                <Text style={styles.twitterDemoButtonText}>Try Demo Mode</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Twitter-style Footer */}
          <View style={styles.twitterFooter}>
            <View style={styles.twitterSwitchContainer}>
              <Text style={styles.twitterSwitchText}>Don't have an account? </Text>
              <TouchableOpacity
                onPress={() => {
                  hapticFeedback.light();
                  onSwitchToRegister();
                }}
              >
                <Text style={styles.twitterSwitchLink}>Sign up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Demo Selection Modal */}
      <Modal
        visible={showDemoModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDemoModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Choose Demo User Type</Text>
            <Text style={styles.modalSubtitle}>Select which type of user you'd like to demo</Text>

            {/* Regular User Option */}
            <TouchableOpacity
              style={styles.demoOption}
              onPress={() => handleDemoLogin('user')}
              activeOpacity={0.8}
            >
              <View style={[styles.demoOptionIcon, { backgroundColor: GoGoColors.primary + '20' }]}>
                <Ionicons name="person" size={24} color={GoGoColors.primary} />
              </View>
              <View style={styles.demoOptionText}>
                <Text style={styles.demoOptionTitle}>Regular User</Text>
                <Text style={styles.demoOptionDescription}>Browse and watch videos</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
            </TouchableOpacity>

            {/* Creator Option */}
            <TouchableOpacity
              style={styles.demoOption}
              onPress={() => handleDemoLogin('creator')}
              activeOpacity={0.8}
            >
              <View style={[styles.demoOptionIcon, { backgroundColor: GoGoColors.highlightGold + '20' }]}>
                <Ionicons name="videocam" size={24} color={GoGoColors.highlightGold} />
              </View>
              <View style={styles.demoOptionText}>
                <Text style={styles.demoOptionTitle}>Content Creator</Text>
                <Text style={styles.demoOptionDescription}>Upload and manage content</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
            </TouchableOpacity>

            {/* Admin Option */}
            <TouchableOpacity
              style={styles.demoOption}
              onPress={() => handleDemoLogin('admin')}
              activeOpacity={0.8}
            >
              <View style={[styles.demoOptionIcon, { backgroundColor: GoGoColors.error + '20' }]}>
                <Ionicons name="shield-checkmark" size={24} color={GoGoColors.error} />
              </View>
              <View style={styles.demoOptionText}>
                <Text style={styles.demoOptionTitle}>Administrator</Text>
                <Text style={styles.demoOptionDescription}>Manage platform and users</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
            </TouchableOpacity>

            {/* Cancel Button */}
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setShowDemoModal(false)}
              activeOpacity={0.8}
            >
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  // Twitter-style Header
  twitterHeader: {
    alignItems: 'center',
    marginBottom: 48,
  },
  twitterLogoContainer: {
    alignItems: 'center',
  },
  twitterLogo: {
    borderRadius: 50,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.highlightGold,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  twitterLogoGradient: {
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  twitterLogoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  // Twitter-style Main Content
  twitterMainContent: {
    flex: 1,
  },
  twitterWelcomeSection: {
    marginBottom: 32,
  },
  twitterMainTitle: {
    fontSize: 31,
    fontWeight: 'bold',
    color: '#0F1419',
    lineHeight: 36,
    marginBottom: 8,
    textAlign: 'center',
  },
  // Twitter-style Form
  twitterFormContainer: {
    marginBottom: 20,
  },
  // Twitter-style Input
  twitterInputContainer: {
    position: 'relative',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#CFD9DE',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  twitterInputContainerFocused: {
    borderColor: GoGoColors.highlightGold,
    borderWidth: 2,
  },
  twitterInput: {
    fontSize: 17,
    color: '#0F1419',
    paddingHorizontal: 12,
    paddingTop: 24,
    paddingBottom: 8,
    minHeight: 56,
  },
  twitterInputLabel: {
    position: 'absolute',
    left: 12,
    top: 16,
    fontSize: 17,
    color: '#536471',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 4,
  },
  twitterInputLabelActive: {
    top: -8,
    fontSize: 13,
    color: GoGoColors.highlightGold,
  },
  twitterPasswordToggle: {
    position: 'absolute',
    right: 12,
    top: 18,
    padding: 4,
  },
  // Twitter-style Error
  twitterErrorContainer: {
    backgroundColor: '#FEF7F7',
    borderWidth: 1,
    borderColor: '#F4212E',
    borderRadius: 4,
    padding: 12,
    marginBottom: 20,
  },
  twitterErrorText: {
    color: '#F4212E',
    fontSize: 15,
    lineHeight: 20,
  },
  // Twitter-style Buttons
  twitterLoginButton: {
    backgroundColor: GoGoColors.highlightGold,
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 44,
  },
  twitterDisabledButton: {
    backgroundColor: '#8B98A5',
  },
  twitterLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  twitterButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: 'bold',
  },
  // Twitter-style Forgot Password
  twitterForgotPassword: {
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  twitterForgotPasswordText: {
    color: GoGoColors.highlightGold,
    fontSize: 15,
    lineHeight: 20,
  },
  // Twitter-style Divider
  twitterDividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  twitterDividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#CFD9DE',
  },
  twitterDividerText: {
    marginHorizontal: 16,
    fontSize: 15,
    color: '#536471',
  },
  // Twitter-style Demo Button
  twitterDemoButton: {
    borderWidth: 1,
    borderColor: '#CFD9DE',
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 44,
    backgroundColor: '#FFFFFF',
  },
  twitterDemoButtonText: {
    color: '#0F1419',
    fontSize: 17,
    fontWeight: 'bold',
  },
  // Twitter-style Footer
  twitterFooter: {
    marginTop: 40,
    alignItems: 'center',
  },
  twitterSwitchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  twitterSwitchText: {
    fontSize: 15,
    color: '#536471',
    lineHeight: 20,
  },
  twitterSwitchLink: {
    fontSize: 15,
    color: GoGoColors.highlightGold,
    fontWeight: '400',
    lineHeight: 20,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  demoOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E1E8ED',
  },
  demoOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  demoOptionText: {
    flex: 1,
  },
  demoOptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  demoOptionDescription: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  modalCancelButton: {
    marginTop: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
});
