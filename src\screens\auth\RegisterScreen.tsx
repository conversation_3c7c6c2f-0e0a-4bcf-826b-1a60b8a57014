import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Picker } from '@react-native-picker/picker';
import { Ionicons } from '@expo/vector-icons';
import { useAppDispatch, useAppSelector } from '../../store';
import { signUp, clearError } from '../../store/slices/authSlice';

import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

interface Props {
  onSwitchToLogin: () => void;
}

export default function RegisterScreen({ onSwitchToLogin }: Props) {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    fullName: '',
    userType: 'user' as 'user' | 'creator' | 'admin',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const handleRegister = async () => {
    // Validation
    if (!formData.email || !formData.password || !formData.username) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return;
    }

    hapticFeedback.medium();

    try {
      await dispatch(signUp(formData)).unwrap();
      hapticFeedback.success();
    } catch (error: any) {
      hapticFeedback.error();
      Alert.alert('Registration Failed', typeof error === 'string' ? error : error?.message || 'An error occurred');
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) {
      dispatch(clearError());
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Twitter-style Header */}
          <View style={styles.twitterHeader}>
            <View style={styles.twitterLogoContainer}>
              {/* GoGo Logo with Twitter-style design */}
              <View style={styles.twitterLogo}>
                <LinearGradient
                  colors={[GoGoColors.highlightGold, '#FFD700']}
                  style={styles.twitterLogoGradient}
                >
                  <Text style={styles.twitterLogoText}>GoGo</Text>
                </LinearGradient>
              </View>
            </View>
          </View>

          {/* Twitter-style Main Content */}
          <View style={styles.twitterMainContent}>
            {/* Twitter-style Welcome Text */}
            <View style={styles.twitterWelcomeSection}>
              <Text style={styles.twitterMainTitle}>Create your account</Text>
            </View>

            {/* Twitter-style Form */}
            <View style={styles.twitterFormContainer}>
              {/* Username Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'username' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.username}
                    onChangeText={(text) => updateFormData('username', text)}
                    onFocus={() => {
                      setFocusedField('username');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'username' || formData.username) && styles.twitterInputLabelActive
                  ]}>
                    Username
                  </Text>
                </View>
              </View>

              {/* Email Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'email' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.email}
                    onChangeText={(text) => updateFormData('email', text)}
                    onFocus={() => {
                      setFocusedField('email');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'email' || formData.email) && styles.twitterInputLabelActive
                  ]}>
                    Email
                  </Text>
                </View>
              </View>

              {/* Password Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'password' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.password}
                    onChangeText={(text) => updateFormData('password', text)}
                    onFocus={() => {
                      setFocusedField('password');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'password' || formData.password) && styles.twitterInputLabelActive
                  ]}>
                    Password
                  </Text>
                  <TouchableOpacity
                    style={styles.twitterPasswordToggle}
                    onPress={() => {
                      setShowPassword(!showPassword);
                      hapticFeedback.light();
                    }}
                  >
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color="#536471"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Confirm Password Input - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'confirmPassword' && styles.twitterInputContainerFocused
                ]}>
                  <TextInput
                    style={styles.twitterInput}
                    placeholder=""
                    placeholderTextColor="#536471"
                    value={formData.confirmPassword}
                    onChangeText={(text) => updateFormData('confirmPassword', text)}
                    onFocus={() => {
                      setFocusedField('confirmPassword');
                      hapticFeedback.light();
                    }}
                    onBlur={() => setFocusedField(null)}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                  />
                  <Text style={[
                    styles.twitterInputLabel,
                    (focusedField === 'confirmPassword' || formData.confirmPassword) && styles.twitterInputLabelActive
                  ]}>
                    Confirm password
                  </Text>
                  <TouchableOpacity
                    style={styles.twitterPasswordToggle}
                    onPress={() => {
                      setShowConfirmPassword(!showConfirmPassword);
                      hapticFeedback.light();
                    }}
                  >
                    <Ionicons
                      name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
                      size={20}
                      color="#536471"
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Account Type Picker - Twitter Style */}
              <View>
                <View style={[
                  styles.twitterInputContainer,
                  focusedField === 'userType' && styles.twitterInputContainerFocused
                ]}>
                  <Picker
                    selectedValue={formData.userType}
                    onValueChange={(value) => {
                      updateFormData('userType', value);
                      hapticFeedback.light();
                    }}
                    style={styles.twitterPicker}
                    dropdownIconColor="#536471"
                  >
                    <Picker.Item label="Regular User" value="user" color="#0F1419" />
                    <Picker.Item label="Content Creator" value="creator" color="#0F1419" />
                    <Picker.Item label="Administrator" value="admin" color="#0F1419" />
                  </Picker>
                  <Text style={[styles.twitterInputLabel, styles.twitterInputLabelActive]}>
                    Account type
                  </Text>
                </View>
              </View>

              {/* Error Display - Twitter Style */}
              {error && (
                <View style={styles.twitterErrorContainer}>
                  <Text style={styles.twitterErrorText}>{error}</Text>
                </View>
              )}

              {/* Twitter-style Sign Up Button */}
              <View>
                <TouchableOpacity
                  style={[styles.twitterRegisterButton, isLoading && styles.twitterDisabledButton]}
                  onPress={handleRegister}
                  disabled={isLoading}
                  activeOpacity={0.8}
                >
                  {isLoading ? (
                    <View style={styles.twitterLoadingContainer}>
                      <ActivityIndicator size="small" color="#FFFFFF" />
                      <Text style={styles.twitterButtonText}>Creating account...</Text>
                    </View>
                  ) : (
                    <Text style={styles.twitterButtonText}>Sign up</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Twitter-style Footer */}
            <View style={styles.twitterFooter}>
              <View style={styles.twitterSwitchContainer}>
                <Text style={styles.twitterSwitchText}>Already have an account? </Text>
                <TouchableOpacity
                  onPress={() => {
                    hapticFeedback.light();
                    onSwitchToLogin();
                  }}
                >
                  <Text style={styles.twitterSwitchLink}>Sign in</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  // Twitter-style Header
  twitterHeader: {
    alignItems: 'center',
    marginBottom: 48,
  },
  twitterLogoContainer: {
    alignItems: 'center',
  },
  twitterLogo: {
    borderRadius: 50,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.highlightGold,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  twitterLogoGradient: {
    width: 80,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  twitterLogoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 1,
  },
  // Twitter-style Main Content
  twitterMainContent: {
    flex: 1,
  },
  twitterWelcomeSection: {
    marginBottom: 32,
  },
  twitterMainTitle: {
    fontSize: 31,
    fontWeight: 'bold',
    color: '#0F1419',
    lineHeight: 36,
    marginBottom: 8,
    textAlign: 'center',
  },
  // Twitter-style Form
  twitterFormContainer: {
    marginBottom: 20,
  },
  // Twitter-style Input
  twitterInputContainer: {
    position: 'relative',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#CFD9DE',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  twitterInputContainerFocused: {
    borderColor: GoGoColors.highlightGold,
    borderWidth: 2,
  },
  twitterInput: {
    fontSize: 17,
    color: '#0F1419',
    paddingHorizontal: 12,
    paddingTop: 24,
    paddingBottom: 8,
    minHeight: 56,
  },
  twitterInputLabel: {
    position: 'absolute',
    left: 12,
    top: 16,
    fontSize: 17,
    color: '#536471',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 4,
  },
  twitterInputLabelActive: {
    top: -8,
    fontSize: 13,
    color: GoGoColors.highlightGold,
  },
  twitterPasswordToggle: {
    position: 'absolute',
    right: 12,
    top: 18,
    padding: 4,
  },
  // Twitter-style Picker
  twitterPicker: {
    fontSize: 17,
    color: '#0F1419',
    paddingHorizontal: 8,
    paddingTop: 20,
    paddingBottom: 4,
    minHeight: 56,
  },
  // Twitter-style Error
  twitterErrorContainer: {
    backgroundColor: '#FEF7F7',
    borderWidth: 1,
    borderColor: '#F4212E',
    borderRadius: 4,
    padding: 12,
    marginBottom: 20,
  },
  twitterErrorText: {
    color: '#F4212E',
    fontSize: 15,
    lineHeight: 20,
  },
  // Twitter-style Buttons
  twitterRegisterButton: {
    backgroundColor: GoGoColors.highlightGold,
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 44,
  },
  twitterDisabledButton: {
    backgroundColor: '#8B98A5',
  },
  twitterLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  twitterButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: 'bold',
  },
  // Twitter-style Footer
  twitterFooter: {
    marginTop: 40,
    alignItems: 'center',
  },
  twitterSwitchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  twitterSwitchText: {
    fontSize: 15,
    color: '#536471',
    lineHeight: 20,
  },
  twitterSwitchLink: {
    fontSize: 15,
    color: GoGoColors.highlightGold,
    fontWeight: '400',
    lineHeight: 20,
  },
});
