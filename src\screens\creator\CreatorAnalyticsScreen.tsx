import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

const mockAnalytics = {
  videoPerformance: [
    { id: '1', title: 'Funny Cat Compilation', views: 12000, likes: 800 },
    { id: '2', title: 'Epic Prank', views: 9500, likes: 600 },
    { id: '3', title: 'Music Video', views: 8700, likes: 700 },
  ],
  subscriberGrowth: [120, 150, 180, 220, 260, 320, 400], // per month
  revenueTrend: [20000, 35000, 50000, 70000, 90000, 120000, 150000], // MWK per month
  months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
};

interface Props {
  onClose: () => void;
}

export default function CreatorAnalyticsScreen({ onClose }: Props) {
  const HEADER_HEIGHT = 100;
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Analytics</Text>
              <Text style={styles.headerSubtitle}>Your channel performance</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      <FlatList
        data={mockAnalytics.videoPerformance}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingTop: HEADER_HEIGHT, paddingBottom: 32, ...styles.listContent }}
        ListHeaderComponent={
          <>
            {/* Subscriber Growth */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Subscriber Growth</Text>
              <View style={styles.analyticsRow}>
                {mockAnalytics.subscriberGrowth.map((val, idx) => (
                  <View key={idx} style={styles.analyticsBarContainer}>
                    <View style={[styles.analyticsBar, { height: val, backgroundColor: GoGoColors.primary }]} />
                    <Text style={styles.analyticsBarLabel}>{mockAnalytics.months[idx]}</Text>
                  </View>
                ))}
              </View>
            </View>
            {/* Revenue Trend */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Revenue Trend (MWK)</Text>
              <View style={styles.analyticsRow}>
                {mockAnalytics.revenueTrend.map((val, idx) => (
                  <View key={idx} style={styles.analyticsBarContainer}>
                    <View style={[styles.analyticsBar, { height: val / 1000, backgroundColor: GoGoColors.warning }]} />
                    <Text style={styles.analyticsBarLabel}>{mockAnalytics.months[idx]}</Text>
                  </View>
                ))}
              </View>
            </View>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Video Performance</Text>
            </View>
          </>
        }
        renderItem={({ item }) => (
          <View style={styles.videoCard}>
            <Ionicons name="videocam" size={24} color={GoGoColors.primary} style={{ marginRight: 12 }} />
            <View style={{ flex: 1 }}>
              <Text style={styles.videoTitle}>{item.title}</Text>
              <Text style={styles.videoViews}>{item.views.toLocaleString()} views • {item.likes} likes</Text>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="videocam-outline" size={48} color={GoGoColors.textMuted} />
            <Text style={styles.emptyText}>No videos found.</Text>
          </View>
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  analyticsRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
    minHeight: 80,
  },
  analyticsBarContainer: {
    flex: 1,
    alignItems: 'center',
  },
  analyticsBar: {
    width: 18,
    borderRadius: 8,
    marginBottom: 6,
  },
  analyticsBarLabel: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  videoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  videoTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  videoViews: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
  },
  listContent: {
    paddingBottom: 24,
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
}); 