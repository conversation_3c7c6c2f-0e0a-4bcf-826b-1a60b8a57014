import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

const mockEarnings = {
  totalEarnings: 450000, // Gross
  monthlyEarnings: 120000,
  platformFeePercent: 20, // Mock platform fee percent
  revenueBreakdown: [
    { label: 'Subscriptions', amount: 300000 },
    { label: 'PPV Sales', amount: 100000 },
    { label: 'Tips', amount: 50000 },
  ],
  payoutRequests: [
    { id: '1', amount: 50000, date: '2024-06-01', status: 'Pending' },
    { id: '2', amount: 30000, date: '2024-05-15', status: 'Completed' },
  ],
};

const statusColors = {
  Completed: GoGoColors.success,
  Pending: GoGoColors.warning,
  Failed: GoGoColors.error,
};

interface Props {
  onClose: () => void;
}

export default function EarningsScreen({ onClose }: Props) {
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const HEADER_HEIGHT = 100;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Earnings</Text>
              <Text style={styles.headerSubtitle}>Your revenue and payouts</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      {/* Gross, Fee, Net Payout */}
      <View style={[styles.statsSection, { paddingTop: HEADER_HEIGHT }]}> 
        <View style={styles.statsRow}>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.primary + '10' }]}> 
            <Ionicons name="cash" size={28} color={GoGoColors.primary} />
            <Text style={styles.statsLabel}>Gross Earnings</Text>
            <Text style={styles.statsValue}>MWK {mockEarnings.totalEarnings.toLocaleString()}</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.error + '10' }]}> 
            <Ionicons name="remove-circle" size={28} color={GoGoColors.error} />
            <Text style={styles.statsLabel}>Platform Fee ({mockEarnings.platformFeePercent}%)</Text>
            <Text style={styles.statsValue}>MWK {Math.round(mockEarnings.totalEarnings * mockEarnings.platformFeePercent / 100).toLocaleString()}</Text>
          </View>
          <View style={[styles.statsCard, { backgroundColor: GoGoColors.success + '10' }]}> 
            <Ionicons name="wallet" size={28} color={GoGoColors.success} />
            <Text style={styles.statsLabel}>Net Payout</Text>
            <Text style={styles.statsValue}>MWK {(mockEarnings.totalEarnings - Math.round(mockEarnings.totalEarnings * mockEarnings.platformFeePercent / 100)).toLocaleString()}</Text>
          </View>
        </View>
      </View>
      {/* Revenue Breakdown */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Revenue Breakdown</Text>
        {mockEarnings.revenueBreakdown.map((item, idx) => (
          <View key={idx} style={styles.breakdownRow}>
            <Text style={styles.breakdownLabel}>{item.label}</Text>
            <Text style={styles.breakdownAmount}>MWK {item.amount.toLocaleString()}</Text>
          </View>
        ))}
      </View>
      {/* Withdrawal History & Pending Payouts */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Withdrawal History & Pending Payouts</Text>
        <FlatList
          data={mockEarnings.payoutRequests}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item, index }) => (
            <Animated.View entering={SlideInDown.delay(index * 60)}>
              <TouchableOpacity
                style={styles.payoutCard}
                onPress={() => setSelectedRequest(item)}
                activeOpacity={0.85}
              >
                <View style={styles.payoutLeft}>
                  <Ionicons name="wallet" size={24} color={GoGoColors.primary} />
                </View>
                <View style={styles.payoutCenter}>
                  <Text style={styles.payoutAmount}>MWK {item.amount.toLocaleString()}</Text>
                  <Text style={styles.payoutDate}>{item.date}</Text>
                </View>
                <View style={styles.payoutRight}>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{item.status}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            </Animated.View>
          )}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Ionicons name="wallet-outline" size={48} color={GoGoColors.textMuted} />
              <Text style={styles.emptyText}>No payout requests.</Text>
            </View>
          }
        />
      </View>
      {/* Payout Request Modal */}
      <Modal
        visible={!!selectedRequest}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSelectedRequest(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedRequest && (
              <>
                <Text style={styles.modalTitle}>Payout Request</Text>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Amount</Text>
                  <Text style={styles.modalValue}>MWK {selectedRequest.amount.toLocaleString()}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Date</Text>
                  <Text style={styles.modalValue}>{selectedRequest.date}</Text>
                </View>
                <View style={styles.modalSection}>
                  <Text style={styles.modalLabel}>Status</Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors[selectedRequest.status as keyof typeof statusColors] || GoGoColors.textMuted }]}> 
                    <Text style={styles.statusBadgeText}>{selectedRequest.status}</Text>
                  </View>
                </View>
                <TouchableOpacity style={styles.modalCloseButton} onPress={() => setSelectedRequest(null)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statsSection: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  statsCard: {
    flex: 1,
    borderRadius: 16,
    padding: 18,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  statsLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginTop: 8,
    marginBottom: 2,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  breakdownLabel: {
    fontSize: 15,
    color: GoGoColors.textSecondary,
  },
  breakdownAmount: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    fontWeight: 'bold',
  },
  payoutCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  payoutLeft: {
    marginRight: 14,
  },
  payoutCenter: {
    flex: 1,
  },
  payoutAmount: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  payoutDate: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  payoutRight: {
    alignItems: 'center',
    marginLeft: 10,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 6,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 12,
  },
  modalLabel: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  modalValue: {
    fontSize: 15,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  modalCloseButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 15,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  listContent: {
    paddingBottom: 32,
  },
}); 