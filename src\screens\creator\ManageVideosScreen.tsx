import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  FlatList,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { GoGoColors } from '../../../constants/Colors';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

const mockVideos = [
  {
    id: '1',
    title: 'Funny Cat Compilation',
    thumbnail: 'https://via.placeholder.com/120x80',
    views: 12000,
    likes: 800,
    uploaded: '2024-05-01',
  },
  {
    id: '2',
    title: 'Epic Prank',
    thumbnail: 'https://via.placeholder.com/120x80',
    views: 9500,
    likes: 600,
    uploaded: '2024-05-10',
  },
  {
    id: '3',
    title: 'Music Video',
    thumbnail: 'https://via.placeholder.com/120x80',
    views: 8700,
    likes: 700,
    uploaded: '2024-05-20',
  },
];

interface Props {
  onClose: () => void;
}

export default function ManageVideosScreen({ onClose }: Props) {
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const HEADER_HEIGHT = 100;

  const handleDelete = async (videoId: string) => {
    setIsActionLoading(true);
    setTimeout(() => {
      setIsActionLoading(false);
      setSelectedVideo(null);
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <View style={[styles.headerPlain, { backgroundColor: GoGoColors.primary }]}> 
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose} activeOpacity={0.8}>
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Manage Videos</Text>
              <Text style={styles.headerSubtitle}>Your uploaded content</Text>
            </View>
            <View style={{ width: 36 }} />
          </View>
        </View>
      </Animated.View>
      <FlatList
        data={mockVideos}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingTop: HEADER_HEIGHT, paddingBottom: 32, ...styles.listContent }}
        renderItem={({ item, index }) => (
          <Animated.View entering={SlideInDown.delay(index * 80)}>
            <TouchableOpacity
              style={styles.videoCard}
              onPress={() => setSelectedVideo(item)}
              activeOpacity={0.85}
            >
              <Image source={{ uri: item.thumbnail }} style={styles.videoThumbnail} />
              <View style={styles.videoCenter}>
                <Text style={styles.videoTitle}>{item.title}</Text>
                <Text style={styles.videoMeta}>{item.views.toLocaleString()} views • {item.likes} likes</Text>
                <Text style={styles.videoMeta}>Uploaded {item.uploaded}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={GoGoColors.textMuted} />
            </TouchableOpacity>
          </Animated.View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="videocam-outline" size={48} color={GoGoColors.textMuted} />
            <Text style={styles.emptyText}>No videos found.</Text>
          </View>
        }
        showsVerticalScrollIndicator={false}
      />
      {/* Video Details Modal */}
      <Modal
        visible={!!selectedVideo}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setSelectedVideo(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedVideo && (
              <>
                <Image source={{ uri: selectedVideo.thumbnail }} style={styles.modalThumbnail} />
                <Text style={styles.modalTitle}>{selectedVideo.title}</Text>
                <Text style={styles.modalSubtitle}>{selectedVideo.views.toLocaleString()} views • {selectedVideo.likes} likes</Text>
                <Text style={styles.modalSubtitle}>Uploaded {selectedVideo.uploaded}</Text>
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.error }]}
                    onPress={() => handleDelete(selectedVideo.id)}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <>
                        <Ionicons name="trash" size={18} color="#fff" />
                        <Text style={styles.modalActionText}>Delete</Text>
                      </>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalActionButton, { backgroundColor: GoGoColors.primary }]}
                    onPress={() => setSelectedVideo(null)}
                  >
                    <Ionicons name="create" size={18} color="#fff" />
                    <Text style={styles.modalActionText}>Edit</Text>
                  </TouchableOpacity>
                </View>
                <TouchableOpacity style={styles.modalCloseButton} onPress={() => setSelectedVideo(null)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerPlain: {
    paddingTop: 28,
    paddingBottom: 6,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  videoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  videoThumbnail: {
    width: 60,
    height: 40,
    borderRadius: 8,
    backgroundColor: GoGoColors.backgroundLight,
    marginRight: 14,
  },
  videoCenter: {
    flex: 1,
  },
  videoTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  videoMeta: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  listContent: {
    paddingBottom: 24,
  },
  emptyState: {
    alignItems: 'center',
    marginTop: 60,
  },
  emptyText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalThumbnail: {
    width: 120,
    height: 80,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 16,
    backgroundColor: GoGoColors.backgroundLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    marginBottom: 16,
    textAlign: 'center',
  },
  modalActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 18,
    marginBottom: 8,
  },
  modalActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 16,
    marginBottom: 6,
  },
  modalActionText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  modalCloseButton: {
    marginTop: 10,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 15,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
}); 