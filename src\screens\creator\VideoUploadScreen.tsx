import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Modal,
  Dimensions,
  StatusBar,
  ActivityIndicator,
  Image,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Video, ResizeMode } from 'expo-av';
import Animated, {
  FadeIn,
  SlideInDown,
  SlideInRight,
  SlideInUp,
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';
import { Video as VideoType, Category } from '../../types';

const { width, height } = Dimensions.get('window');

interface Props {
  onClose: () => void;
}

interface UploadProgress {
  percentage: number;
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  message: string;
}

export default function VideoUploadScreen({ onClose }: Props) {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { categories } = useAppSelector((state) => state.video);

  // File selection state
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [selectedThumbnail, setSelectedThumbnail] = useState<any>(null);
  const [videoDuration, setVideoDuration] = useState<number>(0);
  const [videoSize, setVideoSize] = useState<number>(0);

  // Form data
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [price, setPrice] = useState('');
  const [isPremium, setIsPremium] = useState(false);
  const [isPublic, setIsPublic] = useState(true);

  // UI state
  const [currentStep, setCurrentStep] = useState<'select' | 'details' | 'upload'>('select');
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    percentage: 0,
    status: 'idle',
    message: 'Ready to upload'
  });
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [currentTag, setCurrentTag] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  // Animation values
  const progressValue = useSharedValue(0);
  const scaleValue = useSharedValue(1);

  useEffect(() => {
    if (uploadProgress.status === 'uploading') {
      progressValue.value = withSpring(uploadProgress.percentage / 100);
    }
  }, [uploadProgress.percentage]);

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressValue.value, [0, 1], [0, 100], Extrapolate.CLAMP)}%`,
  }));

  const animatedScaleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleValue.value }],
  }));

  const handleVideoSelect = async () => {
    hapticFeedback.light();
    
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'video/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const video = result.assets[0];
        setSelectedVideo(video);
        
        // Get video duration and size
        if (video.size) {
          setVideoSize(video.size);
        }
        
        // Load video to get duration
        const { sound } = await Video.Sound.createAsync({ uri: video.uri });
        const status = await sound.getStatusAsync();
        if (status.isLoaded) {
          setVideoDuration(status.durationMillis || 0);
        }
        sound.unloadAsync();
        
        hapticFeedback.success();
      }
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to select video. Please try again.');
    }
  };

  const handleThumbnailSelect = async () => {
    hapticFeedback.light();
    
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedThumbnail(result.assets[0]);
        hapticFeedback.success();
      }
    } catch (error) {
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to select thumbnail. Please try again.');
    }
  };

  const handleNextStep = () => {
    hapticFeedback.light();
    
    if (currentStep === 'select' && selectedVideo) {
      setCurrentStep('details');
    } else if (currentStep === 'details' && title && categoryId) {
      setCurrentStep('upload');
    }
  };

  const handlePreviousStep = () => {
    hapticFeedback.light();
    
    if (currentStep === 'details') {
      setCurrentStep('select');
    } else if (currentStep === 'upload') {
      setCurrentStep('details');
    }
  };

  const handleAddTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim())) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
      hapticFeedback.light();
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
    hapticFeedback.light();
  };

  const handleUpload = async () => {
    if (!selectedVideo || !title || !categoryId) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsUploading(true);
    setUploadProgress({ percentage: 0, status: 'uploading', message: 'Starting upload...' });

    try {
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setUploadProgress({
          percentage: i,
          status: 'uploading',
          message: i < 50 ? 'Uploading video...' : i < 80 ? 'Processing video...' : 'Finalizing...'
        });
      }

      setUploadProgress({ percentage: 100, status: 'completed', message: 'Upload completed!' });
      hapticFeedback.success();
      
      setTimeout(() => {
        Alert.alert(
          'Success!',
          'Your video has been uploaded successfully.',
          [{ text: 'OK', onPress: onClose }]
        );
      }, 1000);

    } catch (error) {
      setUploadProgress({ percentage: 0, status: 'error', message: 'Upload failed' });
      hapticFeedback.error();
      Alert.alert('Error', 'Failed to upload video. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {['select', 'details', 'upload'].map((step, index) => (
        <View key={step} style={styles.stepContainer}>
          <View style={[
            styles.stepCircle,
            currentStep === step && styles.stepCircleActive,
            ['select', 'details', 'upload'].indexOf(currentStep) > index && styles.stepCircleCompleted
          ]}>
            {['select', 'details', 'upload'].indexOf(currentStep) > index ? (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            ) : (
              <Text style={[
                styles.stepNumber,
                currentStep === step && styles.stepNumberActive
              ]}>
                {index + 1}
              </Text>
            )}
          </View>
          <Text style={[
            styles.stepLabel,
            currentStep === step && styles.stepLabelActive
          ]}>
            {step === 'select' ? 'Select Video' : step === 'details' ? 'Details' : 'Upload'}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderVideoSelection = () => (
    <Animated.View style={[styles.stepContent, animatedScaleStyle]} entering={FadeIn}>
      <View style={styles.uploadArea}>
        {selectedVideo ? (
          <View style={styles.videoPreview}>
            <Video
              source={{ uri: selectedVideo.uri }}
              style={styles.videoPlayer}
              useNativeControls
              resizeMode={ResizeMode.CONTAIN}
              shouldPlay={false}
            />
            <View style={styles.videoInfo}>
              <Text style={styles.videoTitle}>{selectedVideo.name}</Text>
              <Text style={styles.videoMeta}>
                {formatDuration(videoDuration)} • {formatFileSize(videoSize)}
              </Text>
            </View>
            <TouchableOpacity
              style={styles.changeButton}
              onPress={handleVideoSelect}
            >
              <Ionicons name="refresh" size={20} color={GoGoColors.primary} />
              <Text style={styles.changeButtonText}>Change Video</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={handleVideoSelect}
            onPressIn={() => scaleValue.value = withSpring(0.95)}
            onPressOut={() => scaleValue.value = withSpring(1)}
          >
            <LinearGradient
              colors={[GoGoColors.primary + '20', GoGoColors.primary + '10']}
              style={styles.uploadGradient}
            >
              <Ionicons name="videocam" size={48} color={GoGoColors.primary} />
              <Text style={styles.uploadTitle}>Select Video</Text>
              <Text style={styles.uploadSubtitle}>
                Choose a video file to upload
              </Text>
              <Text style={styles.uploadHint}>
                MP4, MOV, AVI up to 2GB
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.thumbnailSection}>
        <Text style={styles.sectionTitle}>Thumbnail</Text>
        <TouchableOpacity
          style={styles.thumbnailButton}
          onPress={handleThumbnailSelect}
        >
          {selectedThumbnail ? (
            <Image source={{ uri: selectedThumbnail.uri }} style={styles.thumbnailImage} />
          ) : (
            <View style={styles.thumbnailPlaceholder}>
              <Ionicons name="image" size={32} color={GoGoColors.textMuted} />
              <Text style={styles.thumbnailText}>Select Thumbnail</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  const renderDetailsForm = () => (
    <Animated.View style={[styles.stepContent, animatedScaleStyle]} entering={SlideInRight}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Video Details</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Title *</Text>
            <TextInput
              style={styles.textInput}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter video title"
              placeholderTextColor={GoGoColors.textMuted}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Describe your video"
              placeholderTextColor={GoGoColors.textMuted}
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Category *</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCategoryModal(true)}
            >
              <Text style={categoryId ? styles.selectButtonText : styles.selectButtonPlaceholder}>
                {categoryId ? categories.find(c => c.id === categoryId)?.name : 'Select category'}
              </Text>
              <Ionicons name="chevron-down" size={20} color={GoGoColors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Tags</Text>
            <View style={styles.tagInputContainer}>
              <TextInput
                style={styles.tagInput}
                value={currentTag}
                onChangeText={setCurrentTag}
                placeholder="Add a tag"
                placeholderTextColor={GoGoColors.textMuted}
                onSubmitEditing={handleAddTag}
              />
              <TouchableOpacity
                style={styles.addTagButton}
                onPress={handleAddTag}
              >
                <Ionicons name="add" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            {tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.tagChip}
                    onPress={() => handleRemoveTag(tag)}
                  >
                    <Text style={styles.tagText}>#{tag}</Text>
                    <Ionicons name="close" size={16} color={GoGoColors.textMuted} />
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Monetization</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Price (optional)</Text>
            <TextInput
              style={styles.textInput}
              value={price}
              onChangeText={setPrice}
              placeholder="0.00"
              placeholderTextColor={GoGoColors.textMuted}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.toggleContainer}>
            <View style={styles.toggleLabel}>
              <Text style={styles.toggleTitle}>Premium Content</Text>
              <Text style={styles.toggleSubtitle}>Mark as premium content</Text>
            </View>
            <TouchableOpacity
              style={[styles.toggleButton, isPremium && styles.toggleButtonActive]}
              onPress={() => setIsPremium(!isPremium)}
            >
              <View style={[styles.toggleThumb, isPremium && styles.toggleThumbActive]} />
            </TouchableOpacity>
          </View>

          <View style={styles.toggleContainer}>
            <View style={styles.toggleLabel}>
              <Text style={styles.toggleTitle}>Public Video</Text>
              <Text style={styles.toggleSubtitle}>Make video visible to everyone</Text>
            </View>
            <TouchableOpacity
              style={[styles.toggleButton, isPublic && styles.toggleButtonActive]}
              onPress={() => setIsPublic(!isPublic)}
            >
              <View style={[styles.toggleThumb, isPublic && styles.toggleThumbActive]} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );

  const renderUploadProgress = () => (
    <Animated.View style={[styles.stepContent, animatedScaleStyle]} entering={SlideInUp}>
      <View style={styles.uploadProgressContainer}>
        <View style={styles.progressHeader}>
          <Ionicons 
            name={uploadProgress.status === 'completed' ? 'checkmark-circle' : 'cloud-upload'} 
            size={48} 
            color={uploadProgress.status === 'completed' ? GoGoColors.success : GoGoColors.primary} 
          />
          <Text style={styles.progressTitle}>
            {uploadProgress.status === 'completed' ? 'Upload Complete!' : 'Uploading Video'}
          </Text>
          <Text style={styles.progressMessage}>{uploadProgress.message}</Text>
        </View>

        <View style={styles.progressBarContainer}>
          <View style={styles.progressBar}>
            <Animated.View style={[styles.progressFill, animatedProgressStyle]} />
          </View>
          <Text style={styles.progressPercentage}>{uploadProgress.percentage}%</Text>
        </View>

        {uploadProgress.status === 'uploading' && (
          <View style={styles.uploadStats}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>File Size</Text>
              <Text style={styles.statValue}>{formatFileSize(videoSize)}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Duration</Text>
              <Text style={styles.statValue}>{formatDuration(videoDuration)}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Quality</Text>
              <Text style={styles.statValue}>HD</Text>
            </View>
          </View>
        )}

        {uploadProgress.status === 'completed' && (
          <TouchableOpacity
            style={styles.doneButton}
            onPress={onClose}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <LinearGradient
          colors={[GoGoColors.primary, GoGoColors.highlightGold]}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity 
              style={styles.backButton} 
              onPress={onClose}
              activeOpacity={0.8}
            >
              <View style={styles.backButtonInner}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </View>
            </TouchableOpacity>
            
            <View style={styles.headerCenter}>
              <Text style={styles.headerTitle}>Upload Video</Text>
              <Text style={styles.headerSubtitle}>Share your content with the world</Text>
            </View>
            
            <View style={styles.headerRight}>
              {currentStep !== 'upload' && (
                <TouchableOpacity 
                  style={styles.nextButton}
                  onPress={handleNextStep}
                  disabled={!selectedVideo || (currentStep === 'details' && (!title || !categoryId))}
                >
                  <Text style={styles.nextButtonText}>Next</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Content */}
      <View style={styles.content}>
        {currentStep === 'select' && renderVideoSelection()}
        {currentStep === 'details' && renderDetailsForm()}
        {currentStep === 'upload' && renderUploadProgress()}
      </View>

      {/* Navigation */}
      {currentStep !== 'select' && (
        <View style={styles.navigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={handlePreviousStep}
          >
            <Ionicons name="arrow-back" size={20} color={GoGoColors.primary} />
            <Text style={styles.navButtonText}>Back</Text>
          </TouchableOpacity>
          
          {currentStep === 'details' && (
            <TouchableOpacity
              style={[styles.uploadButton, isUploading && styles.uploadButtonDisabled]}
              onPress={handleUpload}
              disabled={isUploading}
            >
              {isUploading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <Ionicons name="cloud-upload" size={20} color="#FFFFFF" />
                  <Text style={styles.uploadButtonText}>Upload Video</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Category Modal */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Category</Text>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[styles.categoryOption, categoryId === category.id && styles.categoryOptionSelected]}
                onPress={() => {
                  setCategoryId(category.id);
                  setShowCategoryModal(false);
                  hapticFeedback.success();
                }}
              >
                <Text style={[styles.categoryOptionText, categoryId === category.id && styles.categoryOptionTextSelected]}>
                  {category.name}
                </Text>
                {categoryId === category.id && (
                  <Ionicons name="checkmark" size={20} color={GoGoColors.primary} />
                )}
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowCategoryModal(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  headerGradient: {
    paddingTop: 40,
    paddingBottom: 12,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerRight: {
    width: 60,
  },
  nextButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginTop: 90,
    backgroundColor: GoGoColors.backgroundCard,
    marginHorizontal: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  stepContainer: {
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  stepCircleActive: {
    backgroundColor: GoGoColors.primary,
  },
  stepCircleCompleted: {
    backgroundColor: GoGoColors.success,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textMuted,
  },
  stepNumberActive: {
    color: '#FFFFFF',
  },
  stepLabel: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  stepLabelActive: {
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    flex: 1,
  },
  uploadArea: {
    marginBottom: 24,
  },
  uploadButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  uploadGradient: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: GoGoColors.primary + '30',
    borderStyle: 'dashed',
    borderRadius: 16,
  },
  uploadTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  uploadSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginBottom: 8,
  },
  uploadHint: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  videoPreview: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    overflow: 'hidden',
  },
  videoPlayer: {
    width: '100%',
    height: 200,
  },
  videoInfo: {
    padding: 16,
  },
  videoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  videoMeta: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  changeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: GoGoColors.backgroundLight,
  },
  changeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.primary,
    marginLeft: 8,
  },
  thumbnailSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 12,
  },
  thumbnailButton: {
    width: '100%',
    height: 120,
    borderRadius: 12,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  thumbnailPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: GoGoColors.border,
    borderStyle: 'dashed',
    borderRadius: 12,
  },
  thumbnailText: {
    fontSize: 14,
    color: GoGoColors.textMuted,
    marginTop: 8,
  },
  formSection: {
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: GoGoColors.textPrimary,
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  selectButton: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectButtonText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
  },
  selectButtonPlaceholder: {
    fontSize: 16,
    color: GoGoColors.textMuted,
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tagInput: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: GoGoColors.textPrimary,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    marginRight: 12,
  },
  addTagButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: GoGoColors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.primary + '15',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
    color: GoGoColors.primary,
    fontWeight: '500',
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  toggleLabel: {
    flex: 1,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  toggleSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  toggleButton: {
    width: 50,
    height: 28,
    borderRadius: 14,
    backgroundColor: GoGoColors.backgroundLight,
    padding: 2,
  },
  toggleButtonActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
  },
  toggleThumbActive: {
    transform: [{ translateX: 22 }],
  },
  navigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: GoGoColors.backgroundCard,
    borderTopWidth: 1,
    borderTopColor: GoGoColors.border,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.primary,
    marginLeft: 8,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  uploadProgressContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  progressHeader: {
    alignItems: 'center',
    marginBottom: 40,
  },
  progressTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginTop: 16,
    marginBottom: 8,
  },
  progressMessage: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  progressBarContainer: {
    width: '100%',
    marginBottom: 40,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: GoGoColors.primary,
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    textAlign: 'center',
  },
  uploadStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 40,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  doneButton: {
    backgroundColor: GoGoColors.success,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 25,
  },
  doneButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    marginBottom: 8,
  },
  categoryOptionSelected: {
    backgroundColor: GoGoColors.primary + '15',
    borderColor: GoGoColors.primary,
    borderWidth: 1,
  },
  categoryOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  categoryOptionTextSelected: {
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  modalButton: {
    marginTop: 16,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
}); 