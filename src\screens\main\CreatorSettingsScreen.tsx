import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  TextInput,
  Modal,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  SlideInDown,
  SlideInRight,
} from 'react-native-reanimated';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

const { width, height } = Dimensions.get('window');

interface Props {
  onClose: () => void;
}

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  value?: string | boolean;
  onPress?: () => void;
  type?: 'toggle' | 'input' | 'select' | 'action';
  index: number;
}

function SettingItem({ icon, title, subtitle, value, onPress, type = 'action', index }: SettingItemProps) {
  return (
    <Animated.View entering={SlideInRight.delay(index * 100)}>
      <TouchableOpacity
        style={styles.settingItem}
        onPress={onPress}
        disabled={type === 'toggle'}
        activeOpacity={0.7}
      >
        <View style={styles.settingLeft}>
          <View style={styles.settingIconContainer}>
            <Ionicons name={icon as any} size={20} color={GoGoColors.primary} />
          </View>
          <View style={styles.settingText}>
            <Text style={styles.settingTitle}>{title}</Text>
            {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
          </View>
        </View>
        
        <View style={styles.settingRight}>
          {type === 'toggle' && typeof value === 'boolean' && (
            <Switch
              value={value}
              onValueChange={onPress}
              trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
              thumbColor={value ? '#FFFFFF' : GoGoColors.textMuted}
            />
          )}
          {type === 'input' && typeof value === 'string' && (
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>{value}</Text>
              <Ionicons name="chevron-forward" size={16} color={GoGoColors.textMuted} />
            </View>
          )}
          {type === 'select' && (
            <View style={styles.valueContainer}>
              <Text style={styles.valueText}>{value}</Text>
              <Ionicons name="chevron-forward" size={16} color={GoGoColors.textMuted} />
            </View>
          )}
          {type === 'action' && (
            <Ionicons name="chevron-forward" size={16} color={GoGoColors.textMuted} />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function CreatorSettingsScreen({ onClose }: Props) {
  // Monetization Settings
  const [monthlyPrice, setMonthlyPrice] = useState('1500');
  const [ppvEnabled, setPpvEnabled] = useState(true);
  const [defaultPpvPrice, setDefaultPpvPrice] = useState('300');
  const [subscriptionEnabled, setSubscriptionEnabled] = useState(true);
  const [revenueShare, setRevenueShare] = useState('20'); // Platform takes 20%
  
  // Content Settings
  const [autoPublish, setAutoPublish] = useState(false);
  const [contentRating, setContentRating] = useState('General');
  const [allowComments, setAllowComments] = useState(true);
  const [allowDownloads, setAllowDownloads] = useState(false);
  
  // Notification Settings
  const [newSubscriberNotif, setNewSubscriberNotif] = useState(true);
  const [purchaseNotif, setPurchaseNotif] = useState(true);
  const [commentNotif, setCommentNotif] = useState(true);
  const [earningsNotif, setEarningsNotif] = useState(true);
  
  // Payment Settings
  const [paymentMethod, setPaymentMethod] = useState('Airtel Money');
  const [phoneNumber, setPhoneNumber] = useState('+265 999 123 456');
  const [autoWithdraw, setAutoWithdraw] = useState(false);
  const [withdrawalThreshold, setWithdrawalThreshold] = useState('10000');
  
  // Modal States
  const [showPriceModal, setShowPriceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [currentEditField, setCurrentEditField] = useState('');
  const [tempValue, setTempValue] = useState('');

  const handlePriceEdit = (field: string, currentValue: string) => {
    hapticFeedback.light();
    setCurrentEditField(field);
    setTempValue(currentValue);
    setShowPriceModal(true);
  };

  const handleSavePrice = () => {
    if (tempValue && !isNaN(Number(tempValue))) {
      switch (currentEditField) {
        case 'monthly':
          setMonthlyPrice(tempValue);
          break;
        case 'ppv':
          setDefaultPpvPrice(tempValue);
          break;
        case 'threshold':
          setWithdrawalThreshold(tempValue);
          break;
      }
      setShowPriceModal(false);
      hapticFeedback.success();
      Alert.alert('Success', 'Price updated successfully!');
    } else {
      Alert.alert('Error', 'Please enter a valid amount');
    }
  };

  const handlePaymentMethodChange = () => {
    hapticFeedback.light();
    setShowPaymentModal(true);
  };

  const handleContentRatingChange = () => {
    hapticFeedback.light();
    setShowRatingModal(true);
  };

  const handleSaveSettings = () => {
    hapticFeedback.success();
    Alert.alert(
      'Settings Saved',
      'Your creator settings have been updated successfully!',
      [{ text: 'OK', onPress: onClose }]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      
      {/* Header */}
      <Animated.View style={styles.header} entering={FadeIn}>
        <LinearGradient
          colors={[GoGoColors.primary, GoGoColors.highlightGold]}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Creator Settings</Text>
            <TouchableOpacity style={styles.saveButton} onPress={handleSaveSettings}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Monetization Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(200)}>
          <Text style={styles.sectionTitle}>Monetization</Text>
          
          <SettingItem
            icon="card-outline"
            title="Monthly Subscription"
            subtitle={subscriptionEnabled ? 'Enabled' : 'Disabled'}
            value={subscriptionEnabled}
            onPress={() => setSubscriptionEnabled(!subscriptionEnabled)}
            type="toggle"
            index={0}
          />
          
          {subscriptionEnabled && (
            <SettingItem
              icon="pricetag-outline"
              title="Monthly Price"
              subtitle="Subscription fee per month"
              value={`MWK ${monthlyPrice}`}
              onPress={() => handlePriceEdit('monthly', monthlyPrice)}
              type="input"
              index={1}
            />
          )}
          
          <SettingItem
            icon="play-circle-outline"
            title="Pay-Per-View"
            subtitle={ppvEnabled ? 'Enabled' : 'Disabled'}
            value={ppvEnabled}
            onPress={() => setPpvEnabled(!ppvEnabled)}
            type="toggle"
            index={2}
          />
          
          {ppvEnabled && (
            <SettingItem
              icon="cash-outline"
              title="Default PPV Price"
              subtitle="Default price for new videos"
              value={`MWK ${defaultPpvPrice}`}
              onPress={() => handlePriceEdit('ppv', defaultPpvPrice)}
              type="input"
              index={3}
            />
          )}
          
          <SettingItem
            icon="pie-chart-outline"
            title="Revenue Share"
            subtitle={`Platform takes ${revenueShare}%, you keep ${100 - Number(revenueShare)}%`}
            value={`${revenueShare}%`}
            type="select"
            index={4}
          />
        </Animated.View>

        {/* Content Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(400)}>
          <Text style={styles.sectionTitle}>Content Management</Text>
          
          <SettingItem
            icon="cloud-upload-outline"
            title="Auto-Publish"
            subtitle="Automatically publish uploaded videos"
            value={autoPublish}
            onPress={() => setAutoPublish(!autoPublish)}
            type="toggle"
            index={0}
          />
          
          <SettingItem
            icon="shield-outline"
            title="Content Rating"
            subtitle="Default rating for your content"
            value={contentRating}
            onPress={handleContentRatingChange}
            type="select"
            index={1}
          />
          
          <SettingItem
            icon="chatbubble-outline"
            title="Allow Comments"
            subtitle="Enable comments on your videos"
            value={allowComments}
            onPress={() => setAllowComments(!allowComments)}
            type="toggle"
            index={2}
          />
          
          <SettingItem
            icon="download-outline"
            title="Allow Downloads"
            subtitle="Let subscribers download your videos"
            value={allowDownloads}
            onPress={() => setAllowDownloads(!allowDownloads)}
            type="toggle"
            index={3}
          />
        </Animated.View>

        {/* Notification Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(600)}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <SettingItem
            icon="person-add-outline"
            title="New Subscribers"
            subtitle="Notify when someone subscribes"
            value={newSubscriberNotif}
            onPress={() => setNewSubscriberNotif(!newSubscriberNotif)}
            type="toggle"
            index={0}
          />
          
          <SettingItem
            icon="card-outline"
            title="Purchases"
            subtitle="Notify about video purchases"
            value={purchaseNotif}
            onPress={() => setPurchaseNotif(!purchaseNotif)}
            type="toggle"
            index={1}
          />
          
          <SettingItem
            icon="chatbubble-outline"
            title="Comments"
            subtitle="Notify about new comments"
            value={commentNotif}
            onPress={() => setCommentNotif(!commentNotif)}
            type="toggle"
            index={2}
          />
          
          <SettingItem
            icon="trending-up-outline"
            title="Earnings Updates"
            subtitle="Weekly earnings summary"
            value={earningsNotif}
            onPress={() => setEarningsNotif(!earningsNotif)}
            type="toggle"
            index={3}
          />
        </Animated.View>

        {/* Payment Settings */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(800)}>
          <Text style={styles.sectionTitle}>Payment & Withdrawals</Text>

          <SettingItem
            icon="phone-portrait-outline"
            title="Payment Method"
            subtitle="Mobile money provider"
            value={paymentMethod}
            onPress={handlePaymentMethodChange}
            type="select"
            index={0}
          />

          <SettingItem
            icon="call-outline"
            title="Phone Number"
            subtitle="Mobile money account number"
            value={phoneNumber}
            onPress={() => handlePriceEdit('phone', phoneNumber)}
            type="input"
            index={1}
          />

          <SettingItem
            icon="repeat-outline"
            title="Auto-Withdraw"
            subtitle="Automatically withdraw earnings"
            value={autoWithdraw}
            onPress={() => setAutoWithdraw(!autoWithdraw)}
            type="toggle"
            index={2}
          />

          {autoWithdraw && (
            <SettingItem
              icon="wallet-outline"
              title="Withdrawal Threshold"
              subtitle="Minimum amount for auto-withdrawal"
              value={`MWK ${withdrawalThreshold}`}
              onPress={() => handlePriceEdit('threshold', withdrawalThreshold)}
              type="input"
              index={3}
            />
          )}
        </Animated.View>

        {/* Analytics & Insights */}
        <Animated.View style={styles.section} entering={SlideInDown.delay(1000)}>
          <Text style={styles.sectionTitle}>Analytics & Insights</Text>

          <SettingItem
            icon="analytics-outline"
            title="Creator Analytics"
            subtitle="View detailed performance metrics"
            onPress={() => Alert.alert('Coming Soon', 'Advanced analytics will be available soon!')}
            index={0}
          />

          <SettingItem
            icon="people-outline"
            title="Audience Insights"
            subtitle="Understand your subscriber demographics"
            onPress={() => Alert.alert('Coming Soon', 'Audience insights coming soon!')}
            index={1}
          />

          <SettingItem
            icon="trending-up-outline"
            title="Revenue Reports"
            subtitle="Download detailed revenue reports"
            onPress={() => Alert.alert('Coming Soon', 'Revenue reports coming soon!')}
            index={2}
          />
        </Animated.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Price Edit Modal */}
      <Modal
        visible={showPriceModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowPriceModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View style={styles.priceModal} entering={SlideInDown}>
            <Text style={styles.modalTitle}>
              {currentEditField === 'monthly' && 'Set Monthly Price'}
              {currentEditField === 'ppv' && 'Set PPV Price'}
              {currentEditField === 'threshold' && 'Set Withdrawal Threshold'}
              {currentEditField === 'phone' && 'Update Phone Number'}
            </Text>

            <TextInput
              style={styles.priceInput}
              value={tempValue}
              onChangeText={setTempValue}
              placeholder={currentEditField === 'phone' ? '+265 999 123 456' : 'Enter amount'}
              placeholderTextColor={GoGoColors.textMuted}
              keyboardType={currentEditField === 'phone' ? 'phone-pad' : 'numeric'}
              autoFocus
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowPriceModal(false)}
              >
                <Text style={styles.modalCancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={handleSavePrice}
              >
                <Text style={styles.modalSaveText}>Save</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>

      {/* Payment Method Modal */}
      <Modal
        visible={showPaymentModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowPaymentModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View style={styles.paymentModal} entering={SlideInDown}>
            <Text style={styles.modalTitle}>Select Payment Method</Text>

            {['Airtel Money', 'TNM Mpamba', 'Bank Transfer'].map((method) => (
              <TouchableOpacity
                key={method}
                style={[
                  styles.paymentOption,
                  paymentMethod === method && styles.paymentOptionSelected
                ]}
                onPress={() => {
                  setPaymentMethod(method);
                  setShowPaymentModal(false);
                  hapticFeedback.light();
                }}
              >
                <Ionicons
                  name={
                    method === 'Airtel Money' ? 'phone-portrait-outline' :
                    method === 'TNM Mpamba' ? 'phone-portrait-outline' : 'card-outline'
                  }
                  size={20}
                  color={paymentMethod === method ? GoGoColors.primary : GoGoColors.textSecondary}
                />
                <Text style={[
                  styles.paymentOptionText,
                  paymentMethod === method && styles.paymentOptionTextSelected
                ]}>
                  {method}
                </Text>
                {paymentMethod === method && (
                  <Ionicons name="checkmark" size={20} color={GoGoColors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </Animated.View>
        </View>
      </Modal>

      {/* Content Rating Modal */}
      <Modal
        visible={showRatingModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowRatingModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View style={styles.ratingModal} entering={SlideInDown}>
            <Text style={styles.modalTitle}>Content Rating</Text>

            {[
              { value: 'General', desc: 'Suitable for all audiences' },
              { value: 'Teen', desc: 'Suitable for ages 13+' },
              { value: 'Mature', desc: 'Suitable for ages 18+' },
              { value: 'Adult', desc: 'Adult content only' }
            ].map((rating) => (
              <TouchableOpacity
                key={rating.value}
                style={[
                  styles.ratingOption,
                  contentRating === rating.value && styles.ratingOptionSelected
                ]}
                onPress={() => {
                  setContentRating(rating.value);
                  setShowRatingModal(false);
                  hapticFeedback.light();
                }}
              >
                <View style={styles.ratingInfo}>
                  <Text style={[
                    styles.ratingText,
                    contentRating === rating.value && styles.ratingTextSelected
                  ]}>
                    {rating.value}
                  </Text>
                  <Text style={styles.ratingDesc}>{rating.desc}</Text>
                </View>
                {contentRating === rating.value && (
                  <Ionicons name="checkmark" size={20} color={GoGoColors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  header: {
    marginBottom: 0,
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  saveButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
    marginTop: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: GoGoColors.backgroundCard,
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  settingRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  valueText: {
    fontSize: 14,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  bottomSpacing: {
    height: 40,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  priceModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 350,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  priceInput: {
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: GoGoColors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundLight,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalCancelText: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
    fontWeight: '600',
  },
  modalSaveButton: {
    flex: 1,
    backgroundColor: GoGoColors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalSaveText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  paymentModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: GoGoColors.backgroundLight,
  },
  paymentOptionSelected: {
    backgroundColor: GoGoColors.primary + '20',
    borderWidth: 1,
    borderColor: GoGoColors.primary,
  },
  paymentOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    marginLeft: 12,
    flex: 1,
  },
  paymentOptionTextSelected: {
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  ratingModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
  },
  ratingOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: GoGoColors.backgroundLight,
  },
  ratingOptionSelected: {
    backgroundColor: GoGoColors.primary + '20',
    borderWidth: 1,
    borderColor: GoGoColors.primary,
  },
  ratingInfo: {
    flex: 1,
    marginLeft: 12,
  },
  ratingText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '600',
    marginBottom: 2,
  },
  ratingTextSelected: {
    color: GoGoColors.primary,
  },
  ratingDesc: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
});
