import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Switch,
  Dimensions,
  StatusBar,
  RefreshControl,
  Modal,
  FlatList,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
  SlideInRight,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { signOut, updateProfile } from '../../store/slices/authSlice';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';

const { width, height } = Dimensions.get('window');

// Modern Profile Option Component
interface ModernProfileOptionProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
  index: number;
  variant?: 'default' | 'premium' | 'danger';
}

function ModernProfileOption({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  rightComponent,
  index,
  variant = 'default'
}: ModernProfileOptionProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.98);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const getVariantStyles = () => {
    switch (variant) {
      case 'premium':
        return {
          backgroundColor: GoGoColors.premium + '10',
          borderColor: GoGoColors.premium + '20',
          iconColor: GoGoColors.premium,
        };
      case 'danger':
        return {
          backgroundColor: GoGoColors.error + '10',
          borderColor: GoGoColors.error + '20',
          iconColor: GoGoColors.error,
        };
      default:
        return {
          backgroundColor: GoGoColors.backgroundCard,
          borderColor: GoGoColors.border,
          iconColor: GoGoColors.primary,
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Remove shadow for Premium Subscription button only
  const isPremiumSubscription = variant === 'premium' && title === 'Premium Subscription';

  return (
    <Animated.View
      style={[
        styles.modernOptionItem,
        {
          backgroundColor: variantStyles.backgroundColor,
          borderColor: variantStyles.borderColor,
        },
        animatedStyle,
        isPremiumSubscription && styles.noShadow,
      ]}
      entering={SlideInRight.delay(index * 100)}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
        style={styles.optionTouchable}
      >
        <View style={styles.optionLeft}>
          <View style={[styles.iconContainer, { backgroundColor: variantStyles.iconColor + '15' }]}>
            <Ionicons name={icon as any} size={20} color={variantStyles.iconColor} />
          </View>
          <View style={styles.optionText}>
            <Text style={styles.modernOptionTitle}>{title}</Text>
            {subtitle && <Text style={styles.modernOptionSubtitle}>{subtitle}</Text>}
          </View>
        </View>
        {rightComponent || (showArrow && (
          <Ionicons name="chevron-forward" size={18} color={GoGoColors.textMuted} />
        ))}
      </TouchableOpacity>
    </Animated.View>
  );
}

// Stats Card Component
interface StatsCardProps {
  icon: string;
  value: string;
  label: string;
  color: string;
  index: number;
}

function StatsCard({ icon, value, label, color, index }: StatsCardProps) {
  return (
    <Animated.View style={styles.statsCard} entering={FadeIn.delay(index * 150)}>
      <LinearGradient
        colors={[color + '15', color + '05']}
        style={styles.statsGradient}
      >
        <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon as any} size={20} color={color} />
        </View>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsLabel}>{label}</Text>
      </LinearGradient>
    </Animated.View>
  );
}

export default function ProfileScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  // State management
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoplayEnabled, setAutoplayEnabled] = useState(true);
  const [downloadQuality, setDownloadQuality] = useState('HD');
  const [refreshing, setRefreshing] = useState(false);

  // Creator monetization state
  const [showCreatorDashboard, setShowCreatorDashboard] = useState(false);
  const [showEarnings, setShowEarnings] = useState(false);
  const [showCreatorSettings, setShowCreatorSettings] = useState(false);
  const [monthlySubscriptionPrice, setMonthlySubscriptionPrice] = useState('1500');
  const [creatorEarnings, setCreatorEarnings] = useState({
    totalEarnings: 45000,
    monthlySubscribers: 23,
    ppvSales: 12,
    pendingWithdrawal: 15000,
    lastWithdrawal: '2024-01-15',
    subscriptionRevenue: 34500,
    ppvRevenue: 10500,
  });

  // Animation values
  const headerScale = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    headerScale.value = withSpring(1, { damping: 15 });
  }, []);

  const handleSignOut = () => {
    hapticFeedback.medium();
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out of your GoGo account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            hapticFeedback.success();
            dispatch(signOut());
          }
        },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Creator monetization functions
  const handleCreatorDashboard = () => {
    hapticFeedback.light();
    setShowCreatorDashboard(true);
  };

  const handleEarningsView = () => {
    hapticFeedback.light();
    setShowEarnings(true);
  };

  const handleWithdrawEarnings = () => {
    hapticFeedback.medium();
    Alert.alert(
      'Withdraw Earnings',
      `Withdraw MWK ${creatorEarnings.pendingWithdrawal.toLocaleString()} to your mobile money account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Withdraw',
          onPress: () => {
            Alert.alert('Success', 'Withdrawal request submitted. Funds will be transferred within 24 hours.');
            setCreatorEarnings(prev => ({ ...prev, pendingWithdrawal: 0 }));
          }
        }
      ]
    );
  };

  const handleSetSubscriptionPrice = () => {
    hapticFeedback.light();
    Alert.prompt(
      'Set Monthly Subscription Price',
      'Enter the monthly subscription price in MWK:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Set Price',
          onPress: (price) => {
            if (price && !isNaN(Number(price))) {
              setMonthlySubscriptionPrice(price);
              Alert.alert('Success', `Monthly subscription price set to MWK ${price}`);
            }
          }
        }
      ],
      'plain-text',
      monthlySubscriptionPrice
    );
  };

  // Animation styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      scrollY.value,
      [0, 100],
      [1, 0.9],
      Extrapolate.CLAMP
    );
    return {
      transform: [{ scale }],
    };
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[GoGoColors.primary]}
            tintColor={GoGoColors.primary}
          />
        }
        onScroll={(event) => {
          scrollY.value = event.nativeEvent.contentOffset.y;
        }}
        scrollEventThrottle={16}
      >
        {/* Modern Profile Header */}
        <Animated.View style={[styles.modernHeader, headerAnimatedStyle]} entering={FadeIn}>
          <LinearGradient
            colors={GoGoColors.backgroundGradient}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <Animated.View style={styles.avatarSection} entering={FadeIn.delay(200)}>
                <View style={styles.modernAvatarContainer}>
                  <Image
                    source={{
                      uri: user?.avatar_url || 'https://via.placeholder.com/120x120/1976D2/ffffff?text=' + (user?.full_name?.charAt(0) || 'U')
                    }}
                    style={styles.modernAvatar}
                  />
                  <TouchableOpacity
                    style={styles.modernEditAvatarButton}
                    onPress={() => {
                      hapticFeedback.light();
                      Alert.alert('Coming Soon', 'Avatar editing will be available soon!');
                    }}
                  >
                    <LinearGradient
                      colors={[GoGoColors.primary, GoGoColors.primaryDark]}
                      style={styles.editButtonGradient}
                    >
                      <Ionicons name="camera" size={16} color="#FFFFFF" />
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.modernUserName}>{user?.full_name || 'GoGo User'}</Text>
                  <Text style={styles.modernUserEmail}>{user?.email || '<EMAIL>'}</Text>
                  <View style={styles.userBadge}>
                    <Ionicons
                      name={user?.user_type === 'creator' ? 'videocam' : 'play-circle'}
                      size={14}
                      color={GoGoColors.primary}
                    />
                    <Text style={styles.userBadgeText}>
                      {user?.user_type === 'creator' ? 'Content Creator' : 'Premium Member'}
                    </Text>
                  </View>
                </View>
              </Animated.View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Stats Section - Redesigned */}
        <Animated.View style={styles.statsSection} entering={SlideInDown.delay(400)}>
          <Text style={styles.activityTitle}>Your Activity</Text>
          <FlatList
            data={[
              { icon: 'play-circle', value: '127', label: 'Videos Watched', color: GoGoColors.primary },
              { icon: 'heart', value: '23', label: 'Favorites', color: GoGoColors.error },
              { icon: 'download', value: '8', label: 'Downloads', color: GoGoColors.success },
              { icon: 'time', value: '45h', label: 'Watch Time', color: GoGoColors.highlightGold },
            ]}
            keyExtractor={item => item.label}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.statsList}
            renderItem={({ item, index }) => (
              <Animated.View style={styles.statsCardModern} entering={FadeIn.delay(index * 100)}>
                <LinearGradient
                  colors={[item.color, GoGoColors.highlightGold] as [string, string]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.statsCardGradient}
                >
                  <View style={styles.statsCardIconWrap}>
                    <Ionicons name={item.icon as any} size={32} color={'#fff'} />
                  </View>
                  <Text style={styles.statsCardValue}>{item.value}</Text>
                  <Text style={styles.statsCardLabel}>{item.label}</Text>
                </LinearGradient>
              </Animated.View>
            )}
          />
        </Animated.View>

        {/* Creator Monetization Section */}
        {user?.user_type === 'creator' && (
          <Animated.View style={styles.modernSection} entering={SlideInDown.delay(500)}>
            <Text style={styles.modernSectionTitle}>Creator Monetization</Text>
            <View style={styles.optionsContainer}>
              <ModernProfileOption
                icon="trending-up-outline"
                title="Earnings Dashboard"
                subtitle={`Total: MWK ${creatorEarnings.totalEarnings.toLocaleString()}`}
                onPress={handleEarningsView}
                index={0}
                variant="premium"
              />
              <ModernProfileOption
                icon="wallet-outline"
                title="Withdraw Earnings"
                subtitle={`Available: MWK ${creatorEarnings.pendingWithdrawal.toLocaleString()}`}
                onPress={handleWithdrawEarnings}
                index={1}
                variant="premium"
              />
              <ModernProfileOption
                icon="pricetag-outline"
                title="Subscription Pricing"
                subtitle={`Monthly: MWK ${monthlySubscriptionPrice}`}
                onPress={handleSetSubscriptionPrice}
                index={2}
              />
              <ModernProfileOption
                icon="analytics-outline"
                title="Creator Analytics"
                subtitle={`${creatorEarnings.monthlySubscribers} subscribers`}
                onPress={handleCreatorDashboard}
                index={3}
              />
              <ModernProfileOption
                icon="settings-outline"
                title="Creator Settings"
                subtitle="Manage monetization & content settings"
                onPress={() => setShowCreatorSettings(true)}
                index={4}
              />
            </View>
          </Animated.View>
        )}

        {/* Account Section */}
        <Animated.View style={styles.modernSection} entering={SlideInDown.delay(600)}>
          <Text style={styles.modernSectionTitle}>Account & Subscription</Text>
          <View style={styles.optionsContainer}>
            <ModernProfileOption
              icon="person-outline"
              title="Edit Profile"
              subtitle="Update your personal information"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Profile editing will be available soon!');
              }}
              index={0}
            />
            <ModernProfileOption
              icon="diamond-outline"
              title="Premium Subscription"
              subtitle="Manage your premium plan"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Subscription management will be available soon!');
              }}
              index={1}
              variant="premium"
            />
            <ModernProfileOption
              icon="card-outline"
              title="Payment Methods"
              subtitle="Manage payment options"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Payment management will be available soon!');
              }}
              index={2}
            />
            <ModernProfileOption
              icon="shield-checkmark-outline"
              title="Privacy & Security"
              subtitle="Account security settings"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Privacy settings will be available soon!');
              }}
              index={3}
            />
          </View>
        </Animated.View>

        {/* Preferences Section */}
        <Animated.View style={styles.modernSection} entering={SlideInDown.delay(800)}>
          <Text style={styles.modernSectionTitle}>Preferences</Text>
          <View style={styles.optionsContainer}>
            <ModernProfileOption
              icon="notifications-outline"
              title="Notifications"
              subtitle="Push notifications and alerts"
              showArrow={false}
              rightComponent={
                <Switch
                  value={notificationsEnabled}
                  onValueChange={(value) => {
                    hapticFeedback.light();
                    setNotificationsEnabled(value);
                  }}
                  trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
                  thumbColor="#FFFFFF"
                  ios_backgroundColor={GoGoColors.backgroundLight}
                />
              }
              index={0}
            />
            <ModernProfileOption
              icon="play-outline"
              title="Autoplay"
              subtitle="Automatically play next video"
              showArrow={false}
              rightComponent={
                <Switch
                  value={autoplayEnabled}
                  onValueChange={(value) => {
                    hapticFeedback.light();
                    setAutoplayEnabled(value);
                  }}
                  trackColor={{ false: GoGoColors.backgroundLight, true: GoGoColors.primary }}
                  thumbColor="#FFFFFF"
                  ios_backgroundColor={GoGoColors.backgroundLight}
                />
              }
              index={1}
            />
            <ModernProfileOption
              icon="download-outline"
              title="Download Quality"
              subtitle={`Current: ${downloadQuality} (1080p)`}
              onPress={() => {
                hapticFeedback.light();
                Alert.alert(
                  'Download Quality',
                  'Choose your preferred download quality',
                  [
                    { text: 'SD (480p)', onPress: () => setDownloadQuality('SD') },
                    { text: 'HD (720p)', onPress: () => setDownloadQuality('HD') },
                    { text: 'Full HD (1080p)', onPress: () => setDownloadQuality('Full HD') },
                    { text: 'Cancel', style: 'cancel' },
                  ]
                );
              }}
              index={2}
            />
            <ModernProfileOption
              icon="language-outline"
              title="Language"
              subtitle="English (US)"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Language settings will be available soon!');
              }}
              index={3}
            />
          </View>
        </Animated.View>

        {/* Support Section */}
        <Animated.View style={styles.modernSection} entering={SlideInDown.delay(1000)}>
          <Text style={styles.modernSectionTitle}>Support & Help</Text>
          <View style={styles.optionsContainer}>
            <ModernProfileOption
              icon="help-circle-outline"
              title="Help Center"
              subtitle="Get help and support"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Help center will be available soon!');
              }}
              index={0}
            />
            <ModernProfileOption
              icon="chatbubble-outline"
              title="Contact Us"
              subtitle="Send feedback or report issues"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Contact form will be available soon!');
              }}
              index={1}
            />
            <ModernProfileOption
              icon="document-text-outline"
              title="Terms & Privacy"
              subtitle="Legal information"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'Legal documents will be available soon!');
              }}
              index={2}
            />
            <ModernProfileOption
              icon="star-outline"
              title="Rate GoGo"
              subtitle="Share your experience"
              onPress={() => {
                hapticFeedback.light();
                Alert.alert('Coming Soon', 'App rating will be available soon!');
              }}
              index={3}
            />
          </View>
        </Animated.View>

        {/* Developer Section */}
        <Animated.View style={styles.modernSection} entering={SlideInDown.delay(1100)}>
          <Text style={styles.modernSectionTitle}>Developer Options</Text>
          <View style={styles.optionsContainer}>
            <ModernProfileOption
              icon="person-outline"
              title="Switch to User"
              subtitle="Test regular user experience"
              onPress={() => {
                hapticFeedback.light();
                dispatch(updateProfile({ user_type: 'user' }));
                Alert.alert('Role Changed', 'You are now a regular user. Restart the app to see changes.');
              }}
              index={0}
            />
            <ModernProfileOption
              icon="videocam-outline"
              title="Switch to Creator"
              subtitle="Test creator dashboard"
              onPress={() => {
                hapticFeedback.light();
                dispatch(updateProfile({ user_type: 'creator' }));
                Alert.alert('Role Changed', 'You are now a creator. Restart the app to see changes.');
              }}
              index={1}
            />
            <ModernProfileOption
              icon="shield-outline"
              title="Switch to Admin"
              subtitle="Test admin dashboard"
              onPress={() => {
                hapticFeedback.light();
                dispatch(updateProfile({ user_type: 'admin' }));
                Alert.alert('Role Changed', 'You are now an admin. Restart the app to see changes.');
              }}
              index={2}
            />
          </View>
        </Animated.View>

        {/* Sign Out Section */}
        <Animated.View style={styles.signOutSection} entering={SlideInDown.delay(1200)}>
          <TouchableOpacity style={styles.modernSignOutButton} onPress={handleSignOut}>
            <LinearGradient
              colors={[GoGoColors.error, GoGoColors.error + 'DD']}
              style={styles.signOutGradient}
            >
              <Ionicons name="log-out-outline" size={20} color="#FFFFFF" />
              <Text style={styles.modernSignOutText}>Sign Out</Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Footer */}
        <Animated.View style={styles.modernFooter} entering={FadeIn.delay(1400)}>
          <Text style={styles.modernVersionText}>GoGo v1.0.0</Text>
          <Text style={styles.footerSubtext}>Made with ❤️ for entertainment lovers</Text>
        </Animated.View>
      </ScrollView>

      {/* Earnings Dashboard Modal */}
      <Modal
        visible={showEarnings}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowEarnings(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Earnings Dashboard</Text>
            <TouchableOpacity onPress={() => setShowEarnings(false)}>
              <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Total Earnings Card */}
            <View style={styles.earningsCard}>
              <LinearGradient
                colors={[GoGoColors.primary, GoGoColors.highlightGold]}
                style={styles.earningsGradient}
              >
                <Text style={styles.earningsTitle}>Total Earnings</Text>
                <Text style={styles.earningsAmount}>MWK {creatorEarnings.totalEarnings.toLocaleString()}</Text>
                <Text style={styles.earningsSubtext}>All time revenue</Text>
              </LinearGradient>
            </View>

            {/* Revenue Breakdown */}
            <View style={styles.revenueSection}>
              <Text style={styles.modalSectionTitle}>Revenue Breakdown</Text>

              <View style={styles.revenueItem}>
                <View style={styles.revenueInfo}>
                  <Ionicons name="people-outline" size={24} color={GoGoColors.primary} />
                  <View style={styles.revenueText}>
                    <Text style={styles.revenueLabel}>Subscription Revenue</Text>
                    <Text style={styles.revenueSubtext}>{creatorEarnings.monthlySubscribers} active subscribers</Text>
                  </View>
                </View>
                <Text style={styles.revenueAmount}>MWK {creatorEarnings.subscriptionRevenue.toLocaleString()}</Text>
              </View>

              <View style={styles.revenueItem}>
                <View style={styles.revenueInfo}>
                  <Ionicons name="play-circle-outline" size={24} color={GoGoColors.highlightGold} />
                  <View style={styles.revenueText}>
                    <Text style={styles.revenueLabel}>Pay-Per-View Revenue</Text>
                    <Text style={styles.revenueSubtext}>{creatorEarnings.ppvSales} video purchases</Text>
                  </View>
                </View>
                <Text style={styles.revenueAmount}>MWK {creatorEarnings.ppvRevenue.toLocaleString()}</Text>
              </View>
            </View>

            {/* Withdrawal Section */}
            <View style={styles.withdrawalSection}>
              <Text style={styles.modalSectionTitle}>Withdrawal</Text>

              <View style={styles.withdrawalCard}>
                <View style={styles.withdrawalInfo}>
                  <Text style={styles.withdrawalLabel}>Available for Withdrawal</Text>
                  <Text style={styles.withdrawalAmount}>MWK {creatorEarnings.pendingWithdrawal.toLocaleString()}</Text>
                  <Text style={styles.withdrawalSubtext}>Last withdrawal: {creatorEarnings.lastWithdrawal}</Text>
                </View>

                <TouchableOpacity
                  style={styles.withdrawButton}
                  onPress={handleWithdrawEarnings}
                >
                  <Text style={styles.withdrawButtonText}>Withdraw</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.paymentMethods}>
                <Text style={styles.paymentTitle}>Payment Methods</Text>
                <View style={styles.paymentOption}>
                  <Ionicons name="phone-portrait-outline" size={20} color={GoGoColors.textSecondary} />
                  <Text style={styles.paymentText}>Airtel Money</Text>
                </View>
                <View style={styles.paymentOption}>
                  <Ionicons name="phone-portrait-outline" size={20} color={GoGoColors.textSecondary} />
                  <Text style={styles.paymentText}>TNM Mpamba</Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  scrollContainer: {
    flex: 1,
  },
  // Modern Header Styles
  modernHeader: {
    marginBottom: 20,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 30,
  },
  headerContent: {
    paddingHorizontal: 20,
  },
  avatarSection: {
    alignItems: 'center',
  },
  modernAvatarContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  modernAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: GoGoColors.backgroundLight,
    borderWidth: 4,
    borderColor: '#FFFFFF',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
  },
  modernEditAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  editButtonGradient: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfo: {
    alignItems: 'center',
  },
  modernUserName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 6,
    textAlign: 'center',
  },
  modernUserEmail: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginBottom: 12,
  },
  userBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.primary + '15',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  userBadgeText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.primary,
  },
  // Stats Section
  statsSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statsCard: {
    flex: 1,
    minWidth: (width - 56) / 2,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsGradient: {
    padding: 16,
    alignItems: 'center',
  },
  statsIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statsValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  // Modern Sections
  modernSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  modernSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  optionsContainer: {
    gap: 12,
  },
  // Modern Option Item
  modernOptionItem: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  optionTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  optionText: {
    flex: 1,
  },
  modernOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  modernOptionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  // Sign Out Section
  signOutSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  modernSignOutButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.error,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  signOutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  modernSignOutText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  // Footer
  modernFooter: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  modernVersionText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 14,
    color: GoGoColors.textMuted,
    textAlign: 'center',
  },
  // Legacy styles (to be removed)
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textLight,
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.error,
    marginLeft: 16,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  versionText: {
    fontSize: 14,
    color: GoGoColors.textMuted,
  },
  // Earnings Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  earningsCard: {
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  earningsGradient: {
    padding: 24,
    alignItems: 'center',
  },
  earningsTitle: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  earningsAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  earningsSubtext: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  revenueSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
  },
  revenueItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  revenueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  revenueText: {
    marginLeft: 12,
    flex: 1,
  },
  revenueLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  revenueSubtext: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  revenueAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.primary,
  },
  withdrawalSection: {
    marginBottom: 24,
  },
  withdrawalCard: {
    backgroundColor: GoGoColors.backgroundCard,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
  },
  withdrawalInfo: {
    marginBottom: 16,
  },
  withdrawalLabel: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  withdrawalAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  withdrawalSubtext: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  withdrawButton: {
    backgroundColor: GoGoColors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  withdrawButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  paymentMethods: {
    backgroundColor: GoGoColors.backgroundCard,
    padding: 16,
    borderRadius: 12,
  },
  paymentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
    marginBottom: 12,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  paymentText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginLeft: 12,
  },
  // New styles for modern stats
  activityTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GoGoColors.highlightGold,
    marginBottom: 16,
    paddingLeft: 4,
  },
  statsList: {
    flexDirection: 'row',
    gap: 16,
    paddingBottom: 8,
    paddingLeft: 4,
  },
  statsCardModern: {
    width: 150,
    height: 170,
    borderRadius: 20,
    marginRight: 16,
    backgroundColor: '#fff',
    shadowColor: GoGoColors.highlightGold,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    overflow: 'hidden',
  },
  statsCardGradient: {
    flex: 1,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 10,
  },
  statsCardIconWrap: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: 'rgba(255,255,255,0.18)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statsCardValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  statsCardLabel: {
    fontSize: 15,
    color: 'rgba(255,255,255,0.92)',
    textAlign: 'center',
    fontWeight: '600',
  },
  noShadow: {
    elevation: 0,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
});