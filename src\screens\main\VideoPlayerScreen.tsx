import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
  Image,
  Share,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setPlaying,
  setCurrentTime,
  setDuration,
  resetPlayer,
} from '../../store/slices/playerSlice';
import { fetchVideoById, incrementViewCount, likeVideo, addToWatchLater } from '../../store/slices/videoSlice';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';
import { formatTime, formatViewCount } from '../../utils/formatters';
import { Video } from '../../types/video';

const { width, height } = Dimensions.get('window');

interface Props {
  videoId: string;
  onClose: () => void;
}

export default function VideoPlayerScreen({ videoId, onClose }: Props) {
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.video);
  const { isPlaying, currentTime, duration } = useAppSelector((state) => state.player);
  const { user } = useAppSelector((state) => state.auth);

  // All state hooks must be called before any conditional returns
  const [showControls, setShowControls] = useState(true);
  const [hasViewBeenCounted, setHasViewBeenCounted] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const [subtitles, setSubtitles] = useState(false);
  const [quality, setQuality] = useState('auto');
  const [showSpeedSelector, setShowSpeedSelector] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isInWatchLater, setIsInWatchLater] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [comments, setComments] = useState([
    {
      id: '1',
      user: { username: 'john_doe', avatar: 'https://via.placeholder.com/40' },
      text: 'Amazing video! Really enjoyed the content.',
      timestamp: '2 hours ago',
      likes: 12,
      isLiked: false,
      replies: [
        {
          id: '1-1',
          user: { username: 'jane_smith', avatar: 'https://via.placeholder.com/40' },
          text: 'I totally agree! Great work.',
          timestamp: '1 hour ago',
          likes: 3,
          isLiked: false,
        }
      ]
    },
    {
      id: '2',
      user: { username: 'video_lover', avatar: 'https://via.placeholder.com/40' },
      text: 'This is exactly what I was looking for. Thanks for sharing!',
      timestamp: '4 hours ago',
      likes: 8,
      isLiked: true,
      replies: []
    },
    {
      id: '3',
      user: { username: 'tech_enthusiast', avatar: 'https://via.placeholder.com/40' },
      text: 'Could you make a follow-up video on this topic? Would love to see more details.',
      timestamp: '6 hours ago',
      likes: 15,
      isLiked: false,
      replies: []
    }
  ]);
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [showCreatorProfile, setShowCreatorProfile] = useState(false);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<'none' | 'active' | 'trial' | 'cancelled'>('none');
  const [nextBillingDate, setNextBillingDate] = useState<string | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [creatorVideos, setCreatorVideos] = useState<Video[]>([]);
  const { videos } = useAppSelector((state) => state.video);
  const [showPPVModal, setShowPPVModal] = useState(false);
  const [isPPVUnlocking, setIsPPVUnlocking] = useState(false);
  const [hasPPVAccess, setHasPPVAccess] = useState(false); // mock access state
  const [reactions, setReactions] = useState({ fire: 0, heart: 0, clap: 0, laugh: 0 });
  const [isFollowingCreator, setIsFollowingCreator] = useState(false); // Mock state for following

  // All animated values must be called before any conditional returns
  const controlsOpacity = useSharedValue(1);
  
  // All animated styles must be called before any conditional returns
  const controlsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: controlsOpacity.value,
  }));

  // All effects must be called before any conditional returns
  useEffect(() => {
    dispatch(fetchVideoById(videoId));
    dispatch(resetPlayer());
    return () => dispatch(resetPlayer());
  }, [dispatch, videoId]);

  useEffect(() => {
    if (showControls && isPlaying) {
      const timer = setTimeout(() => {
        controlsOpacity.value = withTiming(0, { duration: 300 });
        setShowControls(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showControls, isPlaying]);

  useEffect(() => {
    if (currentVideo && currentTime > 30 && !hasViewBeenCounted) {
      dispatch(incrementViewCount(currentVideo.id));
      setHasViewBeenCounted(true);
    }
  }, [currentTime, currentVideo, hasViewBeenCounted, dispatch]);

  // Simulate fetching subscription status when modal opens
  useEffect(() => {
    if (showCreatorProfile) {
      // TODO: Replace with real API call
      setIsSubscribing(false);
      setTimeout(() => {
        // Simulate: user is subscribed to this creator
        setSubscriptionStatus('active');
        setNextBillingDate('2024-07-15');
      }, 500);
    }
  }, [showCreatorProfile]);

  // Fetch creator's videos when modal opens
  useEffect(() => {
    if (showCreatorProfile && currentVideo) {
      // Filter all videos by this creator
      setCreatorVideos(videos.filter(v => v.creator_id === currentVideo.creator_id));
    }
  }, [showCreatorProfile, currentVideo, videos]);

  // All function definitions
  const toggleControls = () => {
    hapticFeedback.light();
    if (showControls) {
      controlsOpacity.value = withTiming(0, { duration: 300 });
      setShowControls(false);
    } else {
      controlsOpacity.value = withTiming(1, { duration: 300 });
      setShowControls(true);
    }
  };

  const handleLike = async () => {
    hapticFeedback.medium();
    if (currentVideo && user) {
      await dispatch(likeVideo({ videoId: currentVideo.id, userId: user.id }));
      setIsLiked(!isLiked);
    }
  };

  const handleWatchLater = async () => {
    hapticFeedback.light();
    if (currentVideo && user) {
      await dispatch(addToWatchLater({ videoId: currentVideo.id, userId: user.id }));
      setIsInWatchLater(!isInWatchLater);
      Alert.alert('Added to Watch Later', 'Video saved for later viewing');
    }
  };

  const handleShare = async () => {
    hapticFeedback.light();
    if (currentVideo) {
      try {
        await Share.share({
          message: `Check out this video: ${currentVideo.title}`,
          url: currentVideo.video_url,
        });
      } catch (error) {
        console.error('Error sharing video:', error);
      }
    }
  };

  const toggleSubtitles = () => {
    hapticFeedback.light();
    setSubtitles(!subtitles);
  };

  const handleQualityChange = (newQuality: string) => {
    hapticFeedback.light();
    setQuality(newQuality);
    setShowQualitySelector(false);
  };

  const handlePlaybackSpeedChange = (speed: number) => {
    hapticFeedback.light();
    setPlaybackSpeed(speed);
    setShowSpeedSelector(false);
  };

  const getProgressPercentage = () => {
    if (duration === 0) return 0;
    return (currentTime / duration) * 100;
  };

  const handleSeek = (percentage: number) => {
    if (duration > 0) {
      const seekTime = (percentage * duration) / 100;
      dispatch(setCurrentTime(seekTime));
    }
  };

  const checkAccess = () => {
    if (!currentVideo) return false;
    if (!currentVideo.is_premium) return true;
    return user?.subscription_status === 'active';
  };

  // Comment functions
  const toggleComments = () => {
    hapticFeedback.medium();
    setShowComments(!showComments);
  };

  const handleAddComment = () => {
    if (commentText.trim() && user) {
      const newComment = {
        id: Date.now().toString(),
        user: {
          username: user.username,
          avatar: user.avatar_url || 'https://via.placeholder.com/40'
        },
        text: commentText.trim(),
        timestamp: 'Just now',
        likes: 0,
        isLiked: false,
        replies: []
      };
      setComments([newComment, ...comments]);
      setCommentText('');
      hapticFeedback.light();
    }
  };

  const handleLikeComment = (commentId, isReply = false, parentId = null) => {
    hapticFeedback.light();
    setComments(prevComments =>
      prevComments.map(comment => {
        if (isReply && comment.id === parentId) {
          return {
            ...comment,
            replies: comment.replies.map(reply =>
              reply.id === commentId
                ? {
                    ...reply,
                    isLiked: !reply.isLiked,
                    likes: reply.isLiked ? reply.likes - 1 : reply.likes + 1
                  }
                : reply
            )
          };
        } else if (!isReply && comment.id === commentId) {
          return {
            ...comment,
            isLiked: !comment.isLiked,
            likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
          };
        }
        return comment;
      })
    );
  };

  const handleReply = (commentId) => {
    setReplyingTo(commentId);
    setReplyText('');
  };

  const handleAddReply = (parentId) => {
    if (replyText.trim() && user) {
      const newReply = {
        id: `${parentId}-${Date.now()}`,
        user: {
          username: user.username,
          avatar: user.avatar_url || 'https://via.placeholder.com/40'
        },
        text: replyText.trim(),
        timestamp: 'Just now',
        likes: 0,
        isLiked: false,
      };

      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === parentId
            ? { ...comment, replies: [...comment.replies, newReply] }
            : comment
        )
      );
      setReplyText('');
      setReplyingTo(null);
      hapticFeedback.light();
    }
  };

  const handleSubscribe = async () => {
    setIsSubscribing(true);
    // TODO: Replace with real payment/subscription API
    setTimeout(() => {
      setIsSubscribing(false);
      setSubscriptionStatus('active');
      setNextBillingDate('2024-08-15');
      Alert.alert('Subscription Successful', 'You are now subscribed!');
    }, 2000);
  };

  const handleCancelSubscription = async () => {
    setIsSubscribing(true);
    // TODO: Replace with real cancel API
    setTimeout(() => {
      setIsSubscribing(false);
      setSubscriptionStatus('cancelled');
      setNextBillingDate(null);
      Alert.alert('Subscription Cancelled', 'Your subscription has been cancelled.');
    }, 1500);
  };

  // Helper: is this video PPV and locked?
  const isPPV = currentVideo?.is_ppv;
  const userHasAccess = hasPPVAccess || currentVideo?.is_unlocked || currentVideo?.is_premium || false; // Replace with real access logic

  // Loading state
  if (!currentVideo) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading video...</Text>
      </View>
    );
  }

  // PPV unlock modal
  if (isPPV && !userHasAccess && showPPVModal) {
    return (
      <View style={[styles.loadingContainer, { justifyContent: 'center', alignItems: 'center' }]}> 
        <Modal visible transparent animationType="fade" onRequestClose={() => setShowPPVModal(false)}>
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.7)', justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ backgroundColor: '#fff', borderRadius: 20, padding: 28, width: 320, alignItems: 'center' }}>
              <Ionicons name="cash" size={40} color={GoGoColors.primary} style={{ marginBottom: 12 }} />
              <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 8 }}>Unlock "{currentVideo.title}"</Text>
              <Text style={{ color: GoGoColors.textPrimary, fontSize: 16, marginBottom: 8 }}>Pay MWK {currentVideo.ppv_price} to watch</Text>
              <Text style={{ color: GoGoColors.textMuted, fontSize: 13, marginBottom: 16 }}>One-time access. No subscription required.</Text>
              <TouchableOpacity style={{ backgroundColor: GoGoColors.primary, borderRadius: 12, padding: 12, width: '100%', marginBottom: 12 }}
                disabled={isPPVUnlocking}
                onPress={() => {
                  setIsPPVUnlocking(true);
                  setTimeout(() => {
                    setIsPPVUnlocking(false);
                    setHasPPVAccess(true);
                    setShowPPVModal(false);
                    Alert.alert('Unlocked!', 'You can now watch this video.');
                  }, 2000);
                }}>
                <Text style={{ color: '#fff', fontWeight: 'bold', textAlign: 'center' }}>{isPPVUnlocking ? 'Processing...' : 'Pay & Unlock'}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={{ marginTop: 8 }} onPress={() => setShowPPVModal(false)}>
                <Text style={{ color: GoGoColors.error, fontWeight: 'bold' }}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    );
  }

  // If PPV and not unlocked, show unlock button
  if (isPPV && !userHasAccess) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Text style={styles.accessDeniedTitle}>Pay-Per-View</Text>
        <Text style={styles.accessDeniedText}>
          This video is available for one-time purchase.
        </Text>
        <TouchableOpacity style={styles.upgradeButton} onPress={() => setShowPPVModal(true)}>
          <Text style={styles.upgradeButtonText}>Pay MWK {currentVideo.ppv_price} to Unlock</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // If PPV and unlocked, show 'Watch Now' button and unlocked indicator
  if (isPPV && hasPPVAccess) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Ionicons name="checkmark-circle" size={48} color={GoGoColors.success} style={{ marginBottom: 16 }} />
        <Text style={styles.accessDeniedTitle}>Unlocked!</Text>
        <Text style={styles.accessDeniedText}>
          You have unlocked this video. Enjoy watching!
        </Text>
        <TouchableOpacity style={styles.upgradeButton} onPress={() => setHasPPVAccess(false)}>
          <Text style={styles.upgradeButtonText}>Watch Now</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Access denied state
  if (!checkAccess()) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Text style={styles.accessDeniedTitle}>Premium Content</Text>
        <Text style={styles.accessDeniedText}>
          This video requires a premium subscription to watch.
        </Text>
        <TouchableOpacity style={styles.upgradeButton}>
          <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar hidden />
      
      {/* Video Player */}
      <View style={styles.videoContainer}>
        <TouchableOpacity
          style={styles.videoTouchable}
          activeOpacity={1}
          onPress={toggleControls}
        >
          {/* Netflix-style Video Placeholder */}
          <View style={styles.videoPlaceholder}>
            <Image
              source={{ uri: currentVideo.thumbnail_url || 'https://via.placeholder.com/800x450' }}
              style={styles.videoThumbnail}
              resizeMode="cover"
            />
            <View style={styles.thumbnailOverlay}>
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                style={styles.thumbnailGradient}
              />
            </View>
          </View>

          {/* Netflix-style Controls Overlay */}
          <Animated.View style={[styles.controlsOverlay, controlsAnimatedStyle]}>
            {/* Top Controls Bar */}
            <View style={styles.topControls}>
              <TouchableOpacity style={styles.netflixBackButton} onPress={onClose}>
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>

              <View style={styles.topRightControls}>
                <TouchableOpacity style={styles.netflixControlButton} onPress={() => setShowAdvancedControls(true)}>
                  <Ionicons name="settings-outline" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.netflixControlButton} onPress={handleShare}>
                  <Ionicons name="share-outline" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Netflix-style Center Play Controls */}
            <View style={styles.netflixCenterControls}>
              <TouchableOpacity style={styles.netflixSkipButton} onPress={() => handleSeek(Math.max(0, getProgressPercentage() - 10))}>
                <Ionicons name="play-skip-back" size={32} color="#FFFFFF" />
                <Text style={styles.skipText}>10</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.netflixPlayButton}
                onPress={() => dispatch(setPlaying(!isPlaying))}
              >
                <View style={styles.netflixPlayButtonInner}>
                  <Ionicons
                    name={isPlaying ? 'pause' : 'play'}
                    size={48}
                    color="#000000"
                    style={!isPlaying ? { marginLeft: 4 } : {}}
                  />
                </View>
              </TouchableOpacity>

              <TouchableOpacity style={styles.netflixSkipButton} onPress={() => handleSeek(Math.min(100, getProgressPercentage() + 10))}>
                <Ionicons name="play-skip-forward" size={32} color="#FFFFFF" />
                <Text style={styles.skipText}>10</Text>
              </TouchableOpacity>
            </View>

            {/* Netflix-style Bottom Controls */}
            <View style={styles.netflixBottomControls}>
              {/* Progress Bar */}
              <View style={styles.netflixProgressContainer}>
                <TouchableOpacity
                  style={styles.netflixProgressBar}
                  onPress={(event) => {
                    const { locationX } = event.nativeEvent;
                    const progressBarWidth = width - 40; // Account for padding
                    const percentage = (locationX / progressBarWidth) * 100;
                    handleSeek(Math.max(0, Math.min(100, percentage)));
                  }}
                >
                  <View style={styles.netflixProgressTrack}>
                    <View
                      style={[styles.netflixProgressFill, { width: `${getProgressPercentage()}%` }]}
                    />
                    <View
                      style={[styles.netflixProgressThumb, { left: `${getProgressPercentage()}%` }]}
                    />
                  </View>
                </TouchableOpacity>
              </View>

              {/* Control Buttons Row */}
              <View style={styles.netflixControlsRow}>
                <View style={styles.netflixLeftControls}>
                  <TouchableOpacity style={styles.netflixSmallButton} onPress={() => dispatch(setPlaying(!isPlaying))}>
                    <Ionicons name={isPlaying ? 'pause' : 'play'} size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                  <Text style={styles.netflixTimeText}>
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </Text>
                </View>

                <View style={styles.netflixRightControls}>
                  <TouchableOpacity style={styles.netflixSmallButton} onPress={toggleSubtitles}>
                    <Ionicons
                      name="chatbox-outline"
                      size={20}
                      color={subtitles ? GoGoColors.primary : "#FFFFFF"}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.netflixSmallButton} onPress={() => setShowSpeedSelector(true)}>
                    <Text style={styles.netflixSpeedText}>{playbackSpeed}x</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.netflixSmallButton} onPress={() => setShowQualitySelector(true)}>
                    <Text style={styles.netflixQualityText}>{quality.toUpperCase()}</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.netflixSmallButton}>
                    <Ionicons name="expand-outline" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Video Information Panel */}
      <ScrollView style={styles.videoInfo} showsVerticalScrollIndicator={false}>
        <View style={styles.videoHeader}>
          <Text style={styles.fullVideoTitle}>{currentVideo.title}</Text>
          <Text style={styles.videoStats}>
            {formatViewCount(currentVideo.view_count)} views • {new Date(currentVideo.created_at).toLocaleDateString()}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
            <Ionicons 
              name={isLiked ? "heart" : "heart-outline"} 
              size={24} 
              color={isLiked ? GoGoColors.error : GoGoColors.textPrimary} 
            />
            <Text style={styles.actionButtonText}>{isLiked ? 'Liked' : 'Like'}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleWatchLater}>
            <Ionicons 
              name={isInWatchLater ? "bookmark" : "bookmark-outline"} 
              size={24} 
              color={isInWatchLater ? GoGoColors.primary : GoGoColors.textPrimary} 
            />
            <Text style={styles.actionButtonText}>{isInWatchLater ? 'Saved' : 'Save'}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={24} color={GoGoColors.textPrimary} />
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

        {/* Creator Info */}
        <View style={styles.creatorSection}>
          <TouchableOpacity style={styles.creatorInfo} onPress={() => setShowCreatorProfile(true)}>
            <Image 
              source={{ uri: currentVideo.creator.avatar_url || 'https://via.placeholder.com/50' }}
              style={styles.creatorAvatar}
            />
            <View style={styles.creatorDetails}>
              <Text style={styles.creatorName}>{currentVideo.creator.username}</Text>
              <Text style={styles.creatorSubscribers}>1.2M subscribers</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.followButton, isFollowingCreator && styles.followingButton]}
            onPress={() => setIsFollowingCreator(!isFollowingCreator)}
          >
            <Text style={[styles.followButtonText, isFollowingCreator && styles.followingButtonText]}>
              {isFollowingCreator ? 'Following' : 'Follow'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.subscribeButton} onPress={() => setShowCreatorProfile(true)}>
            <Text style={styles.subscribeText}>Subscribe</Text>
          </TouchableOpacity>
        </View>

        {/* Description */}
        <View style={styles.descriptionSection}>
          <Text style={styles.descriptionTitle}>Description</Text>
          <Text style={styles.descriptionText}>
            {currentVideo.description || 'No description available for this video.'}
          </Text>
        </View>

        {/* Reactions Row */}
        <View style={styles.reactionsRow}>
          {[
            { key: 'fire', emoji: '🔥' },
            { key: 'heart', emoji: '❤️' },
            { key: 'clap', emoji: '👏' },
            { key: 'laugh', emoji: '😂' },
          ].map(({ key, emoji }) => (
            <TouchableOpacity
              key={key}
              style={styles.reactionButton}
              onPress={() => setReactions({ ...reactions, [key]: (reactions[key] || 0) + 1 })}
            >
              <Text style={styles.reactionEmoji}>{emoji}</Text>
              <Text style={styles.reactionCount}>{reactions[key] || 0}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Comments Section */}
        <View style={styles.commentsSection}>
          <TouchableOpacity style={styles.commentsHeader} onPress={toggleComments}>
            <View style={styles.commentsHeaderLeft}>
              <Text style={styles.commentsTitle}>Comments</Text>
              <Text style={styles.commentsCount}>({comments.length})</Text>
            </View>
            <Ionicons
              name={showComments ? "chevron-up" : "chevron-down"}
              size={20}
              color={GoGoColors.textSecondary}
            />
          </TouchableOpacity>

          {showComments && (
            <View style={styles.commentsContainer}>
              {/* Add Comment Input */}
              {user && (
                <View style={styles.addCommentContainer}>
                  <Image
                    source={{ uri: user.avatar_url || 'https://via.placeholder.com/40' }}
                    style={styles.userAvatar}
                  />
                  <View style={styles.commentInputContainer}>
                    <TextInput
                      style={styles.commentInput}
                      placeholder="Add a comment..."
                      placeholderTextColor={GoGoColors.textMuted}
                      value={commentText}
                      onChangeText={setCommentText}
                      multiline
                      maxLength={500}
                    />
                    {commentText.trim() && (
                      <TouchableOpacity style={styles.postButton} onPress={handleAddComment}>
                        <Text style={styles.postButtonText}>Post</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              )}

              {/* Comments List */}
              {comments.map((comment) => (
                <View key={comment.id} style={styles.commentItem}>
                  <Image
                    source={{ uri: comment.user.avatar }}
                    style={styles.commentAvatar}
                  />
                  <View style={styles.commentContent}>
                    <View style={styles.commentHeader}>
                      <Text style={styles.commentUsername}>{comment.user.username}</Text>
                      <Text style={styles.commentTimestamp}>{comment.timestamp}</Text>
                    </View>
                    <Text style={styles.commentText}>{comment.text}</Text>

                    <View style={styles.commentActions}>
                      <TouchableOpacity
                        style={styles.commentAction}
                        onPress={() => handleLikeComment(comment.id)}
                      >
                        <Ionicons
                          name={comment.isLiked ? "heart" : "heart-outline"}
                          size={16}
                          color={comment.isLiked ? GoGoColors.error : GoGoColors.textMuted}
                        />
                        <Text style={[styles.commentActionText, comment.isLiked && { color: GoGoColors.error }]}>
                          {comment.likes}
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.commentAction}
                        onPress={() => handleReply(comment.id)}
                      >
                        <Ionicons name="chatbubble-outline" size={16} color={GoGoColors.textMuted} />
                        <Text style={styles.commentActionText}>Reply</Text>
                      </TouchableOpacity>
                    </View>

                    {/* Reply Input */}
                    {replyingTo === comment.id && (
                      <View style={styles.replyInputContainer}>
                        <TextInput
                          style={styles.replyInput}
                          placeholder={`Reply to ${comment.user.username}...`}
                          placeholderTextColor={GoGoColors.textMuted}
                          value={replyText}
                          onChangeText={setReplyText}
                          multiline
                          maxLength={300}
                          autoFocus
                        />
                        <View style={styles.replyActions}>
                          <TouchableOpacity
                            style={styles.replyCancel}
                            onPress={() => setReplyingTo(null)}
                          >
                            <Text style={styles.replyCancelText}>Cancel</Text>
                          </TouchableOpacity>
                          {replyText.trim() && (
                            <TouchableOpacity
                              style={styles.replyPost}
                              onPress={() => handleAddReply(comment.id)}
                            >
                              <Text style={styles.replyPostText}>Reply</Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    )}

                    {/* Replies */}
                    {comment.replies.length > 0 && (
                      <View style={styles.repliesContainer}>
                        {comment.replies.map((reply) => (
                          <View key={reply.id} style={styles.replyItem}>
                            <Image
                              source={{ uri: reply.user.avatar }}
                              style={styles.replyAvatar}
                            />
                            <View style={styles.replyContent}>
                              <View style={styles.replyHeader}>
                                <Text style={styles.replyUsername}>{reply.user.username}</Text>
                                <Text style={styles.replyTimestamp}>{reply.timestamp}</Text>
                              </View>
                              <Text style={styles.replyText}>{reply.text}</Text>

                              <TouchableOpacity
                                style={styles.replyLikeAction}
                                onPress={() => handleLikeComment(reply.id, true, comment.id)}
                              >
                                <Ionicons
                                  name={reply.isLiked ? "heart" : "heart-outline"}
                                  size={14}
                                  color={reply.isLiked ? GoGoColors.error : GoGoColors.textMuted}
                                />
                                <Text style={[styles.replyLikeText, reply.isLiked && { color: GoGoColors.error }]}>
                                  {reply.likes}
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Speed Selector Modal */}
      {showSpeedSelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.speedModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Playback Speed</Text>
              {[0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0].map((speed) => (
                <TouchableOpacity
                  key={speed}
                  style={[styles.speedOption, playbackSpeed === speed && styles.speedOptionActive]}
                  onPress={() => handlePlaybackSpeedChange(speed)}
                >
                  <Text style={[styles.speedOptionText, playbackSpeed === speed && styles.speedOptionTextActive]}>
                    {speed}x {speed === 1.0 ? '(Normal)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Quality Selector Modal */}
      {showQualitySelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.qualityModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Video Quality</Text>
              {['auto', '1080p', '720p', '480p', '360p'].map((qual) => (
                <TouchableOpacity
                  key={qual}
                  style={[styles.qualityOption, quality === qual && styles.qualityOptionActive]}
                  onPress={() => handleQualityChange(qual)}
                >
                  <Text style={[styles.qualityOptionText, quality === qual && styles.qualityOptionTextActive]}>
                    {qual.toUpperCase()} {qual === 'auto' ? '(Recommended)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Advanced Controls Modal */}
      {showAdvancedControls && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.advancedModal} entering={SlideInDown}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Advanced Controls</Text>
                <TouchableOpacity onPress={() => setShowAdvancedControls(false)}>
                  <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
              </View>

              <View style={styles.advancedControlsContent}>
                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Subtitles</Text>
                  <TouchableOpacity
                    style={[styles.toggleButton, subtitles && styles.toggleButtonActive]}
                    onPress={toggleSubtitles}
                  >
                    <Text style={[styles.toggleButtonText, subtitles && styles.toggleButtonTextActive]}>
                      {subtitles ? 'ON' : 'OFF'}
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Auto-play Next</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Loop Video</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Creator Profile Modal */}
      <Modal
        visible={showCreatorProfile}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreatorProfile(false)}
      >
        <View style={styles.creatorProfileModal}>
          <View style={styles.creatorProfileHeader}>
            <TouchableOpacity onPress={() => setShowCreatorProfile(false)} style={styles.creatorProfileClose}>
              <Ionicons name="close" size={28} color={GoGoColors.textPrimary} />
            </TouchableOpacity>
            <Image
              source={{ uri: currentVideo.creator.avatar_url || 'https://via.placeholder.com/100' }}
              style={styles.creatorProfileAvatar}
            />
            <Text style={styles.creatorProfileName}>{currentVideo.creator.username}</Text>
            <Text style={styles.creatorProfileSubscribers}>1.2M subscribers</Text>
            <TouchableOpacity
              style={[styles.followButton, isFollowingCreator && styles.followingButton]}
              onPress={() => setIsFollowingCreator(!isFollowingCreator)}
            >
              <Text style={[styles.followButtonText, isFollowingCreator && styles.followingButtonText]}>
                {isFollowingCreator ? 'Following' : 'Follow'}
              </Text>
            </TouchableOpacity>
            <Text style={styles.creatorProfileDescription}>{currentVideo.creator.bio || 'No description available.'}</Text>
          </View>
          <View style={styles.creatorProfileBenefitsSection}>
            <Text style={styles.creatorProfileBenefitsTitle}>Benefits of Subscribing</Text>
            <View style={styles.creatorProfileBenefitsList}>
              <View style={styles.creatorProfileBenefitItem}>
                <Ionicons name="checkmark-circle" size={20} color={GoGoColors.primary} />
                <Text style={styles.creatorProfileBenefitText}>Access all videos</Text>
              </View>
              <View style={styles.creatorProfileBenefitItem}>
                <Ionicons name="checkmark-circle" size={20} color={GoGoColors.primary} />
                <Text style={styles.creatorProfileBenefitText}>Exclusive content</Text>
              </View>
              <View style={styles.creatorProfileBenefitItem}>
                <Ionicons name="checkmark-circle" size={20} color={GoGoColors.primary} />
                <Text style={styles.creatorProfileBenefitText}>Support your favorite creator</Text>
              </View>
            </View>
          </View>
          {/* Subscription Button/Status */}
          {subscriptionStatus === 'active' ? (
            <View style={styles.creatorProfileSubscribedBox}>
              <Ionicons name="checkmark-circle" size={22} color={GoGoColors.success} style={{ marginRight: 8 }} />
              <View style={{ flex: 1 }}>
                <Text style={styles.creatorProfileSubscribedText}>Subscribed</Text>
                {nextBillingDate && (
                  <Text style={styles.creatorProfileSubscribedNote}>Renews on {nextBillingDate}</Text>
                )}
              </View>
              <TouchableOpacity
                style={styles.creatorProfileCancelButton}
                onPress={handleCancelSubscription}
                disabled={isSubscribing}
              >
                {isSubscribing ? (
                  <ActivityIndicator color={GoGoColors.error} size="small" />
                ) : (
                  <Text style={styles.creatorProfileCancelText}>Cancel</Text>
                )}
              </TouchableOpacity>
            </View>
          ) : subscriptionStatus === 'cancelled' ? (
            <View style={styles.creatorProfileSubscribedBox}>
              <Ionicons name="close-circle" size={22} color={GoGoColors.error} style={{ marginRight: 8 }} />
              <Text style={styles.creatorProfileSubscribedText}>Subscription Cancelled</Text>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.creatorProfileSubscribeButton}
              onPress={() => setShowPaymentModal(true)}
              disabled={isSubscribing}
            >
              <LinearGradient
                colors={[GoGoColors.primary, GoGoColors.highlightGold] as [string, string]}
                style={styles.creatorProfileSubscribeGradient}
              >
                {isSubscribing ? (
                  <ActivityIndicator color="#fff" style={{ marginRight: 8 }} />
                ) : (
                  <Ionicons name="star" size={22} color="#fff" style={{ marginRight: 8 }} />
                )}
                <Text style={styles.creatorProfileSubscribeText}>Subscribe for MWK 1,500/month</Text>
                <View style={styles.creatorProfileTrialBadge}>
                  <Text style={styles.creatorProfileTrialText}>First month free!</Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          )}
          <Text style={styles.creatorProfileSubscribeNote}>Cancel anytime. Secure payment.</Text>
          <Text style={styles.creatorProfileVideosTitle}>Creator's Videos</Text>
          <FlatList
            data={creatorVideos}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.creatorProfileVideosList}
            ListEmptyComponent={<Text style={styles.creatorProfileNoVideos}>No public videos yet.</Text>}
            renderItem={({ item }) => (
              <View style={{ marginRight: 16, width: 160 }}>
                <Image source={{ uri: item.thumbnail_url }} style={{ width: 160, height: 90, borderRadius: 12 }} />
                {item.is_premium && subscriptionStatus !== 'active' && (
                  <View style={{ position: 'absolute', top: 8, right: 8, backgroundColor: 'rgba(0,0,0,0.7)', borderRadius: 12, padding: 4 }}>
                    <Ionicons name="lock-closed" size={18} color={GoGoColors.primary} />
                  </View>
                )}
                <Text numberOfLines={2} style={{ color: GoGoColors.textPrimary, fontWeight: 'bold', marginTop: 8 }}>{item.title}</Text>
                <Text style={{ color: GoGoColors.textMuted, fontSize: 12 }}>{item.is_premium ? 'Subscriber-only' : 'Public'}</Text>
              </View>
            )}
          />
          {/* Payment Modal (mock) */}
          <Modal
            visible={showPaymentModal}
            animationType="slide"
            transparent
            onRequestClose={() => setShowPaymentModal(false)}
          >
            <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}>
              <View style={{ backgroundColor: '#fff', borderRadius: 20, padding: 24, width: 320, alignItems: 'center' }}>
                <Ionicons name="card" size={40} color={GoGoColors.primary} style={{ marginBottom: 12 }} />
                <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 8 }}>Subscribe to {currentVideo.creator.username}</Text>
                <Text style={{ color: GoGoColors.textPrimary, fontSize: 16, marginBottom: 8 }}>MWK 1,500/month</Text>
                <Text style={{ color: GoGoColors.success, fontWeight: 'bold', marginBottom: 8 }}>First month free!</Text>
                <Text style={{ color: GoGoColors.textMuted, fontSize: 13, marginBottom: 16 }}>Choose payment method:</Text>
                <TouchableOpacity style={{ backgroundColor: GoGoColors.primary, borderRadius: 12, padding: 12, width: '100%', marginBottom: 12 }} onPress={() => {
                  setShowPaymentModal(false);
                  setIsSubscribing(true);
                  setTimeout(() => {
                    setIsSubscribing(false);
                    setSubscriptionStatus('active');
                    setNextBillingDate('2024-08-15');
                    Alert.alert('Subscription Successful', 'You are now subscribed!');
                  }, 2000);
                }}>
                  <Text style={{ color: '#fff', fontWeight: 'bold', textAlign: 'center' }}>Pay with Airtel Money</Text>
                </TouchableOpacity>
                <TouchableOpacity style={{ backgroundColor: GoGoColors.primary, borderRadius: 12, padding: 12, width: '100%' }} onPress={() => {
                  setShowPaymentModal(false);
                  setIsSubscribing(true);
                  setTimeout(() => {
                    setIsSubscribing(false);
                    setSubscriptionStatus('active');
                    setNextBillingDate('2024-08-15');
                    Alert.alert('Subscription Successful', 'You are now subscribed!');
                  }, 2000);
                }}>
                  <Text style={{ color: '#fff', fontWeight: 'bold', textAlign: 'center' }}>Pay with TNM Mpamba</Text>
                </TouchableOpacity>
                <TouchableOpacity style={{ marginTop: 16 }} onPress={() => setShowPaymentModal(false)}>
                  <Text style={{ color: GoGoColors.error, fontWeight: 'bold' }}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingText: {
    color: GoGoColors.textPrimary,
    fontSize: 16,
    marginTop: 10,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
    padding: 20,
  },
  accessDeniedTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  accessDeniedText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  upgradeButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 15,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  // Video Player Styles
  videoContainer: {
    height: height * 0.35,
    backgroundColor: '#000000',
    position: 'relative',
  },
  videoTouchable: {
    width: '100%',
    height: '100%',
  },
  videoPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#000000',
    position: 'relative',
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
  },
  thumbnailOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  thumbnailGradient: {
    flex: 1,
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  // Netflix-style Controls
  topControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
    background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%)',
  },
  netflixBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  topRightControls: {
    flexDirection: 'row',
    gap: 12,
  },
  netflixControlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Netflix Center Controls
  netflixCenterControls: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 60,
    transform: [{ translateY: -40 }],
  },
  netflixSkipButton: {
    alignItems: 'center',
    justifyContent: 'center',
    opacity: 0.8,
  },
  skipText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    fontWeight: 'bold',
  },
  netflixPlayButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  netflixPlayButtonInner: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Netflix Bottom Controls
  netflixBottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 20,
    background: 'linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%)',
  },
  netflixProgressContainer: {
    marginBottom: 16,
  },
  netflixProgressBar: {
    width: '100%',
    height: 20,
    justifyContent: 'center',
  },
  netflixProgressTrack: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    position: 'relative',
  },
  netflixProgressFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: '#E50914', // Netflix red
    borderRadius: 2,
  },
  netflixProgressThumb: {
    position: 'absolute',
    top: -4,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#E50914',
    marginLeft: -6,
  },
  netflixControlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  netflixLeftControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  netflixRightControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  netflixSmallButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  netflixTimeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  netflixSpeedText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  netflixQualityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // Video Info Panel
  videoInfo: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: 24,
  },
  videoHeader: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  fullVideoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 28,
    marginBottom: 8,
  },
  videoStats: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 20,
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
  },
  // Creator Section
  creatorSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  creatorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  creatorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  creatorDetails: {
    flex: 1,
  },
  creatorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  creatorSubscribers: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  subscribeButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
  },
  subscribeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Description Section
  descriptionSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    lineHeight: 20,
  },
  // Comments Section
  commentsSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: GoGoColors.border,
  },
  commentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  commentsHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  commentsCount: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  commentsContainer: {
    marginTop: 16,
  },
  // Add Comment
  addCommentContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  commentInputContainer: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    padding: 12,
    minHeight: 50,
  },
  commentInput: {
    color: GoGoColors.textPrimary,
    fontSize: 14,
    lineHeight: 20,
    maxHeight: 100,
  },
  postButton: {
    alignSelf: 'flex-end',
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 8,
  },
  postButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Comment Item
  commentItem: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  commentAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  commentUsername: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  commentTimestamp: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  commentText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  commentActions: {
    flexDirection: 'row',
    gap: 16,
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  commentActionText: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  // Reply Input
  replyInputContainer: {
    marginTop: 12,
    backgroundColor: GoGoColors.backgroundLight,
    borderRadius: 8,
    padding: 12,
  },
  replyInput: {
    color: GoGoColors.textPrimary,
    fontSize: 14,
    lineHeight: 20,
    maxHeight: 80,
    marginBottom: 8,
  },
  replyActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  replyCancel: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  replyCancelText: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  replyPost: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  replyPostText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // Replies
  repliesContainer: {
    marginTop: 12,
    paddingLeft: 16,
    borderLeftWidth: 2,
    borderLeftColor: GoGoColors.border,
  },
  replyItem: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  replyAvatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  replyContent: {
    flex: 1,
  },
  replyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 2,
  },
  replyUsername: {
    fontSize: 12,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  replyTimestamp: {
    fontSize: 10,
    color: GoGoColors.textMuted,
  },
  replyText: {
    fontSize: 13,
    color: GoGoColors.textSecondary,
    lineHeight: 18,
    marginBottom: 6,
  },
  replyLikeAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    alignSelf: 'flex-start',
  },
  replyLikeText: {
    fontSize: 10,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  // Modal Overlays
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  speedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  speedOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  speedOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  speedOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  speedOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  qualityModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  qualityOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  qualityOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  qualityOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  qualityOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // Advanced Controls Modal
  advancedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  advancedControlsContent: {
    paddingTop: 16,
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  controlLabel: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  toggleButton: {
    backgroundColor: GoGoColors.backgroundLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleButtonText: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    fontWeight: 'bold',
  },
  toggleButtonTextActive: {
    color: '#FFFFFF',
  },
  // Creator Profile Modal
  creatorProfileModal: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    paddingTop: 32,
  },
  creatorProfileHeader: {
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  creatorProfileClose: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
  },
  creatorProfileAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: GoGoColors.primary,
  },
  creatorProfileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  creatorProfileSubscribers: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 8,
  },
  creatorProfileDescription: {
    fontSize: 15,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    marginBottom: 12,
  },
  creatorProfileBenefitsSection: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  creatorProfileBenefitsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  creatorProfileBenefitsList: {
    gap: 10,
  },
  creatorProfileBenefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  creatorProfileBenefitText: {
    fontSize: 14,
    color: GoGoColors.textPrimary,
    marginLeft: 8,
  },
  creatorProfileSubscribeButton: {
    marginHorizontal: 20,
    marginBottom: 8,
    borderRadius: 30,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: GoGoColors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  creatorProfileSubscribeGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 30,
  },
  creatorProfileSubscribeText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
  creatorProfileTrialBadge: {
    backgroundColor: GoGoColors.success,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginLeft: 8,
  },
  creatorProfileTrialText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  creatorProfileSubscribeNote: {
    textAlign: 'center',
    color: GoGoColors.textMuted,
    fontSize: 13,
    marginBottom: 18,
  },
  creatorProfileVideosTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginLeft: 20,
    marginBottom: 8,
  },
  creatorProfileVideosList: {
    paddingLeft: 20,
    paddingBottom: 24,
  },
  creatorProfileNoVideos: {
    color: GoGoColors.textMuted,
    fontSize: 14,
    paddingVertical: 24,
    textAlign: 'center',
  },
  // Add styles for subscription status UI
  creatorProfileSubscribedBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 18,
    marginHorizontal: 20,
    marginBottom: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    gap: 8,
  },
  creatorProfileSubscribedText: {
    color: GoGoColors.success,
    fontWeight: 'bold',
    fontSize: 16,
  },
  creatorProfileSubscribedNote: {
    color: GoGoColors.textMuted,
    fontSize: 13,
    marginTop: 2,
  },
  creatorProfileCancelButton: {
    backgroundColor: GoGoColors.error,
    borderRadius: 12,
    paddingHorizontal: 14,
    paddingVertical: 6,
    marginLeft: 8,
  },
  creatorProfileCancelText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  // Reactions Row
  reactionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: GoGoColors.backgroundCard,
    borderTopWidth: 1,
    borderTopColor: GoGoColors.border,
  },
  reactionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: GoGoColors.backgroundLight,
  },
  reactionEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  reactionCount: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    fontWeight: '500',
  },
  // Follow Button
  followButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 8,
  },
  followingButton: {
    backgroundColor: GoGoColors.textMuted,
  },
  followButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  followingButtonText: {
    color: '#FFFFFF',
  },
});
