import { supabase, TABLES, STORAGE_BUCKETS } from '../config/supabase';
import { notificationService } from './notificationService';

export interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  role: 'viewer' | 'creator' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  video_url: string;
  duration: number;
  price: number; // MWK 300 for pay-per-view
  creator_id: string;
  category_id: string;
  is_free: boolean;
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  creator_id: string;
  status: 'active' | 'expired' | 'cancelled';
  price: number; // MWK 1,500/month
  start_date: string;
  end_date: string;
  created_at: string;
}

export interface Purchase {
  id: string;
  user_id: string;
  video_id: string;
  amount: number;
  payment_method: 'airtel_money' | 'tnm_mpamba';
  transaction_id: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

class DatabaseService {
  // User operations
  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert(userData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  async getUserById(userId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data;
  }

  async updateUser(userId: string, updates: Partial<User>) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Video operations
  async createVideo(videoData: Omit<Video, 'id' | 'created_at' | 'updated_at'>) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.VIDEOS)
      .insert(videoData)
      .select()
      .single();
    
    if (error) throw error;

    // Notify users about new video
    await notificationService.notifyNewVideo(videoData.creator_id, videoData.title, data.id);
    
    return data;
  }

  async getVideos(limit = 20, offset = 0) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.VIDEOS)
      .select(`
        *,
        creator:users!creator_id(username, full_name, avatar_url),
        category:categories(name)
      `)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  async getVideoById(videoId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.VIDEOS)
      .select(`
        *,
        creator:users!creator_id(username, full_name, avatar_url),
        category:categories(name)
      `)
      .eq('id', videoId)
      .single();
    
    if (error) throw error;
    return data;
  }

  async getVideosByCreator(creatorId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.VIDEOS)
      .select('*')
      .eq('creator_id', creatorId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  // Subscription operations
  async createSubscription(subscriptionData: Omit<Subscription, 'id' | 'created_at'>) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .insert(subscriptionData)
      .select()
      .single();
    
    if (error) throw error;

    // Get creator info for notification
    const creator = await this.getUserById(subscriptionData.creator_id);
    await notificationService.notifySubscriptionCreated(subscriptionData.user_id, creator.full_name);
    
    return data;
  }

  async getUserSubscriptions(userId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select(`
        *,
        creator:users!creator_id(username, full_name, avatar_url)
      `)
      .eq('user_id', userId)
      .eq('status', 'active');
    
    if (error) throw error;
    return data;
  }

  async checkUserSubscription(userId: string, creatorId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select('*')
      .eq('user_id', userId)
      .eq('creator_id', creatorId)
      .eq('status', 'active')
      .gte('end_date', new Date().toISOString())
      .single();
    
    if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
    return data;
  }

  // Purchase operations
  async createPurchase(purchaseData: Omit<Purchase, 'id' | 'created_at'>) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .insert(purchaseData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  async updatePurchaseStatus(purchaseId: string, status: Purchase['status']) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .update({ status })
      .eq('id', purchaseId)
      .select()
      .single();
    
    if (error) throw error;

    // Notify user about payment status
    if (status === 'completed') {
      await notificationService.notifyPaymentSuccess(
        data.user_id,
        data.amount,
        'Video purchase'
      );
    }
    
    return data;
  }

  async getUserPurchases(userId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .select(`
        *,
        video:videos(title, thumbnail_url, duration)
      `)
      .eq('user_id', userId)
      .eq('status', 'completed')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  async checkUserPurchase(userId: string, videoId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .select('*')
      .eq('user_id', userId)
      .eq('video_id', videoId)
      .eq('status', 'completed')
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  // Analytics for creators
  async getCreatorAnalytics(creatorId: string) {
    if (!supabase) throw new Error('Supabase not initialized');
    
    // Get total revenue from subscriptions
    const { data: subscriptionRevenue } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select('price')
      .eq('creator_id', creatorId)
      .eq('status', 'active');

    // Get total revenue from video purchases
    const { data: purchaseRevenue } = await supabase
      .from(TABLES.PURCHASES)
      .select('amount')
      .eq('status', 'completed')
      .in('video_id', 
        supabase
          .from(TABLES.VIDEOS)
          .select('id')
          .eq('creator_id', creatorId)
      );

    // Get subscriber count
    const { count: subscriberCount } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select('*', { count: 'exact', head: true })
      .eq('creator_id', creatorId)
      .eq('status', 'active');

    // Get video count
    const { count: videoCount } = await supabase
      .from(TABLES.VIDEOS)
      .select('*', { count: 'exact', head: true })
      .eq('creator_id', creatorId);

    const totalSubscriptionRevenue = subscriptionRevenue?.reduce((sum, sub) => sum + sub.price, 0) || 0;
    const totalPurchaseRevenue = purchaseRevenue?.reduce((sum, purchase) => sum + purchase.amount, 0) || 0;

    return {
      totalRevenue: totalSubscriptionRevenue + totalPurchaseRevenue,
      subscriptionRevenue: totalSubscriptionRevenue,
      purchaseRevenue: totalPurchaseRevenue,
      subscriberCount: subscriberCount || 0,
      videoCount: videoCount || 0,
    };
  }
}

export const databaseService = new DatabaseService();
