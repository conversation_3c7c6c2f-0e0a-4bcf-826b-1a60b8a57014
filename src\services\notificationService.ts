import { ref, push, onValue, off, query, orderByChild, limitToLast } from 'firebase/database';
import { database, NOTIFICATION_PATHS, NOTIFICATION_TYPES } from '../config/firebase';

export interface Notification {
  id?: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  read: boolean;
  userId?: string;
  creatorId?: string;
}

class NotificationService {
  private listeners: Map<string, any> = new Map();

  // Send notification to a specific user
  async sendUserNotification(userId: string, notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
    try {
      const userNotificationsRef = ref(database, NOTIFICATION_PATHS.USER_NOTIFICATIONS(userId));
      const newNotification: Omit<Notification, 'id'> = {
        ...notification,
        timestamp: Date.now(),
        read: false,
        userId,
      };
      
      await push(userNotificationsRef, newNotification);
      console.log('User notification sent successfully');
    } catch (error) {
      console.error('Error sending user notification:', error);
      throw error;
    }
  }

  // Send notification to a specific creator
  async sendCreatorNotification(creatorId: string, notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
    try {
      const creatorNotificationsRef = ref(database, NOTIFICATION_PATHS.CREATOR_NOTIFICATIONS(creatorId));
      const newNotification: Omit<Notification, 'id'> = {
        ...notification,
        timestamp: Date.now(),
        read: false,
        creatorId,
      };
      
      await push(creatorNotificationsRef, newNotification);
      console.log('Creator notification sent successfully');
    } catch (error) {
      console.error('Error sending creator notification:', error);
      throw error;
    }
  }

  // Send global notification (to all users)
  async sendGlobalNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
    try {
      const globalNotificationsRef = ref(database, NOTIFICATION_PATHS.GLOBAL_NOTIFICATIONS);
      const newNotification: Omit<Notification, 'id'> = {
        ...notification,
        timestamp: Date.now(),
        read: false,
      };
      
      await push(globalNotificationsRef, newNotification);
      console.log('Global notification sent successfully');
    } catch (error) {
      console.error('Error sending global notification:', error);
      throw error;
    }
  }

  // Send admin notification
  async sendAdminNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
    try {
      const adminNotificationsRef = ref(database, NOTIFICATION_PATHS.ADMIN_NOTIFICATIONS);
      const newNotification: Omit<Notification, 'id'> = {
        ...notification,
        timestamp: Date.now(),
        read: false,
      };
      
      await push(adminNotificationsRef, newNotification);
      console.log('Admin notification sent successfully');
    } catch (error) {
      console.error('Error sending admin notification:', error);
      throw error;
    }
  }

  // Listen to user notifications
  listenToUserNotifications(userId: string, callback: (notifications: Notification[]) => void) {
    const userNotificationsRef = ref(database, NOTIFICATION_PATHS.USER_NOTIFICATIONS(userId));
    const notificationsQuery = query(userNotificationsRef, orderByChild('timestamp'), limitToLast(50));
    
    const listener = onValue(notificationsQuery, (snapshot) => {
      const notifications: Notification[] = [];
      snapshot.forEach((childSnapshot) => {
        const notification = childSnapshot.val() as Notification;
        notification.id = childSnapshot.key!;
        notifications.unshift(notification); // Most recent first
      });
      callback(notifications);
    });

    this.listeners.set(`user_${userId}`, listener);
    return listener;
  }

  // Listen to creator notifications
  listenToCreatorNotifications(creatorId: string, callback: (notifications: Notification[]) => void) {
    const creatorNotificationsRef = ref(database, NOTIFICATION_PATHS.CREATOR_NOTIFICATIONS(creatorId));
    const notificationsQuery = query(creatorNotificationsRef, orderByChild('timestamp'), limitToLast(50));
    
    const listener = onValue(notificationsQuery, (snapshot) => {
      const notifications: Notification[] = [];
      snapshot.forEach((childSnapshot) => {
        const notification = childSnapshot.val() as Notification;
        notification.id = childSnapshot.key!;
        notifications.unshift(notification); // Most recent first
      });
      callback(notifications);
    });

    this.listeners.set(`creator_${creatorId}`, listener);
    return listener;
  }

  // Listen to global notifications
  listenToGlobalNotifications(callback: (notifications: Notification[]) => void) {
    const globalNotificationsRef = ref(database, NOTIFICATION_PATHS.GLOBAL_NOTIFICATIONS);
    const notificationsQuery = query(globalNotificationsRef, orderByChild('timestamp'), limitToLast(20));
    
    const listener = onValue(notificationsQuery, (snapshot) => {
      const notifications: Notification[] = [];
      snapshot.forEach((childSnapshot) => {
        const notification = childSnapshot.val() as Notification;
        notification.id = childSnapshot.key!;
        notifications.unshift(notification); // Most recent first
      });
      callback(notifications);
    });

    this.listeners.set('global', listener);
    return listener;
  }

  // Listen to admin notifications
  listenToAdminNotifications(callback: (notifications: Notification[]) => void) {
    const adminNotificationsRef = ref(database, NOTIFICATION_PATHS.ADMIN_NOTIFICATIONS);
    const notificationsQuery = query(adminNotificationsRef, orderByChild('timestamp'), limitToLast(50));
    
    const listener = onValue(notificationsQuery, (snapshot) => {
      const notifications: Notification[] = [];
      snapshot.forEach((childSnapshot) => {
        const notification = childSnapshot.val() as Notification;
        notification.id = childSnapshot.key!;
        notifications.unshift(notification); // Most recent first
      });
      callback(notifications);
    });

    this.listeners.set('admin', listener);
    return listener;
  }

  // Stop listening to notifications
  stopListening(key: string) {
    const listener = this.listeners.get(key);
    if (listener) {
      off(listener);
      this.listeners.delete(key);
    }
  }

  // Stop all listeners
  stopAllListeners() {
    this.listeners.forEach((listener) => {
      off(listener);
    });
    this.listeners.clear();
  }

  // Helper methods for common notification types
  async notifyNewVideo(creatorId: string, videoTitle: string, videoId: string) {
    await this.sendGlobalNotification({
      type: NOTIFICATION_TYPES.NEW_VIDEO,
      title: 'New Video Available!',
      message: `${videoTitle} has been uploaded`,
      data: { videoId, creatorId },
    });
  }

  async notifyPaymentSuccess(userId: string, amount: number, description: string) {
    await this.sendUserNotification(userId, {
      type: NOTIFICATION_TYPES.PAYMENT_SUCCESS,
      title: 'Payment Successful',
      message: `Your payment of MWK ${amount} for ${description} was successful`,
      data: { amount, description },
    });
  }

  async notifySubscriptionCreated(userId: string, creatorName: string) {
    await this.sendUserNotification(userId, {
      type: NOTIFICATION_TYPES.SUBSCRIPTION_CREATED,
      title: 'Subscription Active',
      message: `You are now subscribed to ${creatorName}`,
      data: { creatorName },
    });
  }

  async notifyCreatorApproved(creatorId: string) {
    await this.sendCreatorNotification(creatorId, {
      type: NOTIFICATION_TYPES.CREATOR_APPROVED,
      title: 'Creator Account Approved',
      message: 'Congratulations! Your creator account has been approved. You can now start uploading content.',
      data: {},
    });
  }
}

export const notificationService = new NotificationService();
export { NOTIFICATION_TYPES };
