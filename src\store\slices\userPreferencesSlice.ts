import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { supabase } from '../../config/supabase';
import { Video, User } from '../../types';

interface Playlist {
  id: string;
  name: string;
  description: string;
  videos: Video[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

interface UserPreferences {
  watchLater: Video[];
  playlists: Playlist[];
  favoriteCreators: User[];
  viewingHistory: {
    video: Video;
    progress: number;
    lastWatched: string;
  }[];
  contentPreferences: {
    categories: string[];
    languages: string[];
    quality: 'auto' | '1080p' | '720p' | '480p';
    autoplay: boolean;
    notifications: boolean;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: UserPreferences = {
  watchLater: [],
  playlists: [],
  favoriteCreators: [],
  viewingHistory: [],
  contentPreferences: {
    categories: [],
    languages: ['en'],
    quality: 'auto',
    autoplay: true,
    notifications: true,
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchUserPreferences = createAsyncThunk(
  'userPreferences/fetchUserPreferences',
  async (userId: string, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateContentPreferences = createAsyncThunk(
  'userPreferences/updateContentPreferences',
  async (
    { userId, preferences }: { userId: string; preferences: Partial<UserPreferences['contentPreferences']> },
    { rejectWithValue }
  ) => {
    try {
      if (!supabase) {
        // Demo mode - return updated preferences
        return { ...initialState.contentPreferences, ...preferences };
      }

      const { data, error } = await supabase
        .from('user_preferences')
        .update({ content_preferences: preferences })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return data.content_preferences;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const addToWatchLater = createAsyncThunk(
  'userPreferences/addToWatchLater',
  async ({ userId, video }: { userId: string; video: Video }, { rejectWithValue }) => {
    try {
      if (!supabase) {
        // Demo mode - return updated watch later list
        return [...initialState.watchLater, video];
      }

      const { data, error } = await supabase
        .from('watch_later')
        .insert({ user_id: userId, video_id: video.id })
        .select('video:video_id(*)')
        .single();

      if (error) throw error;

      return data.video;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const removeFromWatchLater = createAsyncThunk(
  'userPreferences/removeFromWatchLater',
  async ({ userId, videoId }: { userId: string; videoId: string }, { rejectWithValue }) => {
    try {
      if (!supabase) {
        // Demo mode - return updated watch later list
        return initialState.watchLater.filter(v => v.id !== videoId);
      }

      const { error } = await supabase
        .from('watch_later')
        .delete()
        .eq('user_id', userId)
        .eq('video_id', videoId);

      if (error) throw error;

      return videoId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createPlaylist = createAsyncThunk(
  'userPreferences/createPlaylist',
  async (
    { userId, playlist }: { userId: string; playlist: Omit<Playlist, 'id' | 'created_at' | 'updated_at'> },
    { rejectWithValue }
  ) => {
    try {
      if (!supabase) {
        // Demo mode - return mock playlist
        return {
          ...playlist,
          id: Math.random().toString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      }

      const { data, error } = await supabase
        .from('playlists')
        .insert({
          user_id: userId,
          name: playlist.name,
          description: playlist.description,
          is_public: playlist.is_public,
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const addToPlaylist = createAsyncThunk(
  'userPreferences/addToPlaylist',
  async (
    { playlistId, video }: { playlistId: string; video: Video },
    { rejectWithValue }
  ) => {
    try {
      if (!supabase) {
        // Demo mode - return updated playlist
        return { playlistId, video };
      }

      const { data, error } = await supabase
        .from('playlist_videos')
        .insert({ playlist_id: playlistId, video_id: video.id })
        .select('video:video_id(*)')
        .single();

      if (error) throw error;

      return { playlistId, video: data.video };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const removeFromPlaylist = createAsyncThunk(
  'userPreferences/removeFromPlaylist',
  async (
    { playlistId, videoId }: { playlistId: string; videoId: string },
    { rejectWithValue }
  ) => {
    try {
      if (!supabase) {
        // Demo mode - return updated playlist
        return { playlistId, videoId };
      }

      const { error } = await supabase
        .from('playlist_videos')
        .delete()
        .eq('playlist_id', playlistId)
        .eq('video_id', videoId);

      if (error) throw error;

      return { playlistId, videoId };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const toggleFavoriteCreator = createAsyncThunk(
  'userPreferences/toggleFavoriteCreator',
  async ({ userId, creator }: { userId: string; creator: User }, { rejectWithValue }) => {
    try {
      if (!supabase) {
        // Demo mode - return updated favorite creators list
        return creator;
      }

      const { data, error } = await supabase
        .from('favorite_creators')
        .upsert({ user_id: userId, creator_id: creator.id })
        .select('creator:creator_id(*)')
        .single();

      if (error) throw error;

      return data.creator;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateViewingHistory = createAsyncThunk(
  'userPreferences/updateViewingHistory',
  async (
    { userId, video, progress }: { userId: string; video: Video; progress: number },
    { rejectWithValue }
  ) => {
    try {
      if (!supabase) {
        // Demo mode - return updated viewing history
        return { video, progress, lastWatched: new Date().toISOString() };
      }

      const { data, error } = await supabase
        .from('viewing_history')
        .upsert({
          user_id: userId,
          video_id: video.id,
          progress,
          last_watched: new Date().toISOString(),
        })
        .select('video:video_id(*)')
        .single();

      if (error) throw error;

      return {
        video: data.video,
        progress,
        lastWatched: new Date().toISOString(),
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const userPreferencesSlice = createSlice({
  name: 'userPreferences',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch User Preferences
      .addCase(fetchUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        return { ...state, ...action.payload };
      })
      .addCase(fetchUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update Content Preferences
      .addCase(updateContentPreferences.fulfilled, (state, action) => {
        state.contentPreferences = { ...state.contentPreferences, ...action.payload };
      })
      // Watch Later
      .addCase(addToWatchLater.fulfilled, (state, action) => {
        state.watchLater.push(action.payload);
      })
      .addCase(removeFromWatchLater.fulfilled, (state, action) => {
        state.watchLater = state.watchLater.filter(v => v.id !== action.payload);
      })
      // Playlists
      .addCase(createPlaylist.fulfilled, (state, action) => {
        state.playlists.push(action.payload);
      })
      .addCase(addToPlaylist.fulfilled, (state, action) => {
        const playlist = state.playlists.find(p => p.id === action.payload.playlistId);
        if (playlist) {
          playlist.videos.push(action.payload.video);
        }
      })
      .addCase(removeFromPlaylist.fulfilled, (state, action) => {
        const playlist = state.playlists.find(p => p.id === action.payload.playlistId);
        if (playlist) {
          playlist.videos = playlist.videos.filter(v => v.id !== action.payload.videoId);
        }
      })
      // Favorite Creators
      .addCase(toggleFavoriteCreator.fulfilled, (state, action) => {
        const index = state.favoriteCreators.findIndex(c => c.id === action.payload.id);
        if (index === -1) {
          state.favoriteCreators.push(action.payload);
        } else {
          state.favoriteCreators.splice(index, 1);
        }
      })
      // Viewing History
      .addCase(updateViewingHistory.fulfilled, (state, action) => {
        const index = state.viewingHistory.findIndex(h => h.video.id === action.payload.video.id);
        if (index === -1) {
          state.viewingHistory.push(action.payload);
        } else {
          state.viewingHistory[index] = action.payload;
        }
        // Sort by last watched
        state.viewingHistory.sort((a, b) => 
          new Date(b.lastWatched).getTime() - new Date(a.lastWatched).getTime()
        );
      });
  },
});

export const { clearError } = userPreferencesSlice.actions;
export default userPreferencesSlice.reducer;
