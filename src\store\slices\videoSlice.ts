import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { supabase } from '../../config/supabase';
import { VideoState, Video, Category, PaginatedResponse } from '../../types';
import { databaseService } from '../../services/databaseService';

const initialState: VideoState = {
  videos: [],
  currentVideo: null,
  categories: [],
  isLoading: false,
  error: null,
  searchResults: [],
  recommendations: [],
};

// Async thunks
export const fetchVideos = createAsyncThunk(
  'video/fetchVideos',
  async ({ page = 1, limit = 20, categoryId }: { page?: number; limit?: number; categoryId?: string }, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      let query = supabase
        .from('videos')
        .select(`
          *,
          creator:users(id, username, full_name, avatar_url),
          category:categories(id, name)
        `)
        .order('created_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data as Video[],
        total: count || 0,
        page,
        limit,
        hasMore: (count || 0) > page * limit,
      } as PaginatedResponse<Video>;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchVideoById = createAsyncThunk(
  'video/fetchVideoById',
  async (videoId: string, { rejectWithValue }) => {
    try {
      // Demo mode - use mock data if Supabase not configured
      if (!supabase) {
        const video = mockVideos.find(v => v.id === videoId);
        if (!video) {
          throw new Error('Video not found');
        }
        return video;
      }

      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          creator:users(id, username, full_name, avatar_url),
          category:categories(id, name)
        `)
        .eq('id', videoId)
        .single();

      if (error) throw error;

      return data as Video;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const searchVideos = createAsyncThunk(
  'video/searchVideos',
  async (query: string, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          creator:users(id, username, full_name, avatar_url),
          category:categories(id, name)
        `)
        .or(`title.ilike.%${query}%, description.ilike.%${query}%, tags.cs.{${query}}`)
        .order('view_count', { ascending: false })
        .limit(50);

      if (error) throw error;

      return data as Video[];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'video/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;

      return data as Category[];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchRecommendations = createAsyncThunk(
  'video/fetchRecommendations',
  async (userId: string, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      // Simple recommendation algorithm - can be improved later
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          creator:users(id, username, full_name, avatar_url),
          category:categories(id, name)
        `)
        .order('view_count', { ascending: false })
        .limit(20);

      if (error) throw error;

      return data as Video[];
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const incrementViewCount = createAsyncThunk(
  'video/incrementViewCount',
  async (videoId: string, { rejectWithValue }) => {
    try {
      // Demo mode - just return success if Supabase not configured
      if (!supabase) {
        return videoId;
      }

      const { error } = await supabase.rpc('increment_view_count', {
        video_id: videoId,
      });

      if (error) throw error;

      return videoId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const likeVideo = createAsyncThunk(
  'video/likeVideo',
  async ({ videoId, userId }: { videoId: string; userId: string }, { rejectWithValue }) => {
    try {
      if (!supabase) {
        throw new Error('Database not configured. Please set up Supabase connection.');
      }

      // Check if already liked
      const { data: existingLike } = await supabase
        .from('video_likes')
        .select('id')
        .eq('video_id', videoId)
        .eq('user_id', userId)
        .single();

      if (existingLike) {
        // Unlike
        await supabase
          .from('video_likes')
          .delete()
          .eq('video_id', videoId)
          .eq('user_id', userId);

        await supabase.rpc('decrement_like_count', { video_id: videoId });

        return { videoId, liked: false };
      } else {
        // Like
        await supabase
          .from('video_likes')
          .insert({ video_id: videoId, user_id: userId });

        await supabase.rpc('increment_like_count', { video_id: videoId });

        return { videoId, liked: true };
      }
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const videoSlice = createSlice({
  name: 'video',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentVideo: (state, action: PayloadAction<Video | null>) => {
      state.currentVideo = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    updateVideoInList: (state, action: PayloadAction<Video>) => {
      const index = state.videos.findIndex(v => v.id === action.payload.id);
      if (index !== -1) {
        state.videos[index] = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Videos
      .addCase(fetchVideos.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchVideos.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.page === 1) {
          state.videos = action.payload.data;
        } else {
          state.videos = [...state.videos, ...action.payload.data];
        }
      })
      .addCase(fetchVideos.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch Video by ID
      .addCase(fetchVideoById.fulfilled, (state, action) => {
        state.currentVideo = action.payload;
      })
      // Search Videos
      .addCase(searchVideos.fulfilled, (state, action) => {
        state.searchResults = action.payload;
      })
      // Fetch Categories
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      // Fetch Recommendations
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.recommendations = action.payload;
      })
      // Like Video
      .addCase(likeVideo.fulfilled, (state, action) => {
        const { videoId, liked } = action.payload;
        const video = state.videos.find(v => v.id === videoId);
        if (video) {
          video.like_count += liked ? 1 : -1;
        }
        if (state.currentVideo?.id === videoId) {
          state.currentVideo.like_count += liked ? 1 : -1;
        }
      });
  },
});

export const { clearError, setCurrentVideo, clearSearchResults, updateVideoInList } = videoSlice.actions;
export default videoSlice.reducer;
