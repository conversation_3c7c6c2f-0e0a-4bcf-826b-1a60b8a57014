// Core Types for GoGo App

export interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  role: 'viewer' | 'creator' | 'admin';
  user_type?: 'user' | 'creator' | 'admin'; // Legacy field for compatibility
  phone_number?: string;
  subscription_status?: 'active' | 'inactive' | 'trial';
  created_at: string;
  updated_at: string;
}

export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  video_url: string;
  duration: number; // in seconds
  creator_id: string;
  creator: User;
  category_id: string;
  category: Category;
  price?: number; // null for free content
  is_premium: boolean;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
  tags: string[];
  quality_options: VideoQuality[];
}

export interface VideoQuality {
  quality: '480p' | '720p' | '1080p' | '4K';
  url: string;
  file_size: number;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  thumbnail_url?: string;
  created_at: string;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  user_id: string;
  videos: Video[];
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface Purchase {
  id: string;
  user_id: string;
  video_id: string;
  amount: number;
  currency: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  stripe_payment_intent_id: string;
  created_at: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  creator_id: string;
  plan_type: 'monthly' | 'yearly';
  amount: number;
  status: 'active' | 'cancelled' | 'expired';
  current_period_start: string;
  current_period_end: string;
  stripe_subscription_id: string;
  created_at: string;
}

export interface CreatorAnalytics {
  total_revenue: number;
  total_views: number;
  total_subscribers: number;
  monthly_revenue: number;
  monthly_views: number;
  top_videos: Video[];
  revenue_by_month: { month: string; revenue: number }[];
}

export interface VideoProgress {
  id: string;
  user_id: string;
  video_id: string;
  progress_seconds: number;
  completed: boolean;
  last_watched: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'new_video' | 'purchase_success' | 'subscription' | 'general';
  read: boolean;
  created_at: string;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  VideoPlayer: { videoId: string };
  CreatorDashboard: undefined;
  Profile: undefined;
  Settings: undefined;
};

export type TabParamList = {
  Home: undefined;
  Search: undefined;
  Library: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  fullName: string;
  userType: 'user' | 'creator' | 'admin';
}

export interface VideoUploadForm {
  title: string;
  description: string;
  categoryId: string;
  tags: string[];
  price?: number;
  isPremium: boolean;
}

// Redux State Types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface VideoState {
  videos: Video[];
  currentVideo: Video | null;
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  searchResults: Video[];
  recommendations: Video[];
}

export interface PlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isFullscreen: boolean;
  quality: string;
  isBuffering: boolean;
}
